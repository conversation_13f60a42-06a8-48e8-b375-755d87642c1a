"""
交易配置管理模块
"""
import json
from datetime import datetime
from database.manager import db_manager
from config import Config
from utils.logger import logger

class TradingConfigManager:
    """交易配置管理器"""
    
    def __init__(self):
        self.default_symbol_configs = self._get_default_symbol_configs()
        self.init_default_configs()
    
    def _get_default_symbol_configs(self):
        """获取默认交易对配置"""
        return {
            "XAUUSD": {
                "enabled": True,
                "lot_size": 0.01,
                "stop_loss_usd": 50.0,
                "take_profit_usd": 100.0,
                "signal_timeout": 180,
                "category": "precious_metals"
            },
            "ETHUSD": {
                "enabled": True,
                "lot_size": 0.1,
                "stop_loss_usd": 30.0,
                "take_profit_usd": 60.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "BTCUSD": {
                "enabled": True,
                "lot_size": 0.01,
                "stop_loss_usd": 100.0,
                "take_profit_usd": 200.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "SOLUSD": {
                "enabled": True,
                "lot_size": 0.1,
                "stop_loss_usd": 25.0,
                "take_profit_usd": 50.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "GBPUSD": {
                "enabled": True,
                "lot_size": 0.1,
                "stop_loss_usd": 40.0,
                "take_profit_usd": 80.0,
                "signal_timeout": 180,
                "category": "forex"
            },
            "GBPJPY": {
                "enabled": True,
                "lot_size": 0.1,
                "stop_loss_usd": 45.0,
                "take_profit_usd": 90.0,
                "signal_timeout": 180,
                "category": "forex"
            },
            "BCHUSD": {
                "enabled": True,
                "lot_size": 0.1,
                "stop_loss_usd": 20.0,
                "take_profit_usd": 40.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "ADAUSD": {
                "enabled": True,
                "lot_size": 1.0,
                "stop_loss_usd": 15.0,
                "take_profit_usd": 30.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "XLMUSD": {
                "enabled": True,
                "lot_size": 1.0,
                "stop_loss_usd": 10.0,
                "take_profit_usd": 20.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "DOGEUSD": {
                "enabled": True,
                "lot_size": 10.0,
                "stop_loss_usd": 15.0,
                "take_profit_usd": 30.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "LINKUSD": {
                "enabled": True,
                "lot_size": 0.5,
                "stop_loss_usd": 20.0,
                "take_profit_usd": 40.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "LTCUSD": {
                "enabled": True,
                "lot_size": 0.1,
                "stop_loss_usd": 25.0,
                "take_profit_usd": 50.0,
                "signal_timeout": 180,
                "category": "crypto"
            },
            "XRPUSD": {
                "enabled": True,
                "lot_size": 5.0,
                "stop_loss_usd": 12.0,
                "take_profit_usd": 24.0,
                "signal_timeout": 180,
                "category": "crypto"
            }
        }
    
    def init_default_configs(self):
        """初始化默认配置"""
        try:
            # 初始化交易对配置
            for symbol, config in self.default_symbol_configs.items():
                existing = db_manager.execute_query(
                    "SELECT id FROM symbol_config WHERE symbol = ?",
                    (symbol,)
                )
                
                if not existing:
                    db_manager.execute_query(
                        """INSERT INTO symbol_config 
                           (symbol, enabled, lot_size, stop_loss_usd, take_profit_usd, signal_timeout, category)
                           VALUES (?, ?, ?, ?, ?, ?, ?)""",
                        (symbol, config["enabled"], config["lot_size"], 
                         config["stop_loss_usd"], config["take_profit_usd"],
                         config["signal_timeout"], config["category"])
                    )
            
            # 初始化系统配置
            system_configs = {
                "trading_cooldown": Config.DEFAULT_TRADING_COOLDOWN,
                "max_concurrent_trades": Config.MAX_CONCURRENT_TRADES,
                "bark_forward_enabled": True,
                "emergency_stop": False,
                "max_daily_loss_usd": Config.MAX_DAILY_LOSS_USD,
                "max_daily_profit_usd": Config.MAX_DAILY_PROFIT_USD,
                "bark_device_1_key": Config.BARK_DEVICE_1_KEY,
                "bark_device_2_key": Config.BARK_DEVICE_2_KEY
            }
            
            for key, value in system_configs.items():
                existing = db_manager.execute_query(
                    "SELECT id FROM config WHERE key = ?",
                    (key,)
                )
                
                if not existing:
                    db_manager.execute_query(
                        "INSERT INTO config (key, value, description) VALUES (?, ?, ?)",
                        (key, str(value), f"系统配置: {key}")
                    )
            
            # 初始化全局止盈止损配置
            existing_portfolio = db_manager.execute_query(
                "SELECT id FROM portfolio_sl_tp LIMIT 1"
            )
            
            if not existing_portfolio:
                db_manager.execute_query(
                    """INSERT INTO portfolio_sl_tp 
                       (enabled, global_stop_loss_usd, global_take_profit_usd, check_interval_seconds)
                       VALUES (?, ?, ?, ?)""",
                    (False, 500.0, 1000.0, 30)
                )
            
            logger.info("默认配置初始化完成")
            
        except Exception as e:
            logger.error(f"初始化默认配置失败: {e}")
    
    def get_symbol_config(self, symbol):
        """获取交易对配置"""
        try:
            config = db_manager.execute_query(
                """SELECT enabled, lot_size, stop_loss_usd, take_profit_usd, 
                          signal_timeout, category
                   FROM symbol_config WHERE symbol = ?""",
                (symbol,)
            )
            
            if config:
                config = config[0]
                return {
                    "enabled": bool(config[0]),
                    "lot_size": float(config[1]),
                    "stop_loss_usd": float(config[2]),
                    "take_profit_usd": float(config[3]),
                    "signal_timeout": int(config[4]),
                    "category": config[5]
                }
            else:
                # 返回默认配置
                return self.default_symbol_configs.get(symbol, {
                    "enabled": False,
                    "lot_size": 0.01,
                    "stop_loss_usd": 50.0,
                    "take_profit_usd": 100.0,
                    "signal_timeout": 180,
                    "category": "unknown"
                })
                
        except Exception as e:
            logger.error(f"获取交易对配置失败: {e}")
            return None
    
    def update_symbol_config(self, symbol, config):
        """更新交易对配置"""
        try:
            db_manager.execute_query(
                """UPDATE symbol_config 
                   SET enabled = ?, lot_size = ?, stop_loss_usd = ?, 
                       take_profit_usd = ?, signal_timeout = ?, category = ?,
                       updated_at = CURRENT_TIMESTAMP
                   WHERE symbol = ?""",
                (config["enabled"], config["lot_size"], config["stop_loss_usd"],
                 config["take_profit_usd"], config["signal_timeout"], 
                 config["category"], symbol)
            )
            
            logger.info(f"交易对配置更新成功: {symbol}")
            return {"success": True, "message": "配置更新成功"}
            
        except Exception as e:
            logger.error(f"更新交易对配置失败: {e}")
            return {"success": False, "message": str(e)}
    
    def get_all_symbol_configs(self):
        """获取所有交易对配置"""
        try:
            configs = db_manager.execute_query(
                """SELECT symbol, enabled, lot_size, stop_loss_usd, 
                          take_profit_usd, signal_timeout, category, updated_at
                   FROM symbol_config ORDER BY symbol"""
            )
            
            result = {}
            for config in configs:
                result[config[0]] = {
                    "enabled": bool(config[1]) if config[1] is not None else False,
                    "lot_size": float(config[2]) if config[2] is not None else 0.01,
                    "stop_loss_usd": float(config[3]) if config[3] is not None else -50.0,
                    "take_profit_usd": float(config[4]) if config[4] is not None else 100.0,
                    "signal_timeout": int(config[5]) if config[5] is not None else 180,
                    "category": config[6] if config[6] is not None else "unknown",
                    "updated_at": config[7]
                }
            
            return result
            
        except Exception as e:
            logger.error(f"获取所有交易对配置失败: {e}")
            return {}
    
    def get_system_config(self, key=None):
        """获取系统配置"""
        try:
            if key:
                config = db_manager.execute_query(
                    "SELECT value FROM config WHERE key = ?",
                    (key,)
                )
                return config[0][0] if config else None
            else:
                configs = db_manager.execute_query(
                    "SELECT key, value FROM config"
                )
                return {config[0]: config[1] for config in configs}
                
        except Exception as e:
            logger.error(f"获取系统配置失败: {e}")
            return None
    
    def update_system_config(self, key, value):
        """更新系统配置"""
        try:
            db_manager.execute_query(
                """INSERT OR REPLACE INTO config (key, value, updated_at)
                   VALUES (?, ?, CURRENT_TIMESTAMP)""",
                (key, str(value))
            )
            
            logger.info(f"系统配置更新成功: {key} = {value}")
            return {"success": True, "message": "配置更新成功"}
            
        except Exception as e:
            logger.error(f"更新系统配置失败: {e}")
            return {"success": False, "message": str(e)}
    
    def get_portfolio_config(self):
        """获取全局组合配置"""
        try:
            config = db_manager.execute_query(
                """SELECT enabled, global_stop_loss_usd, global_take_profit_usd, 
                          check_interval_seconds, last_check_time
                   FROM portfolio_sl_tp ORDER BY id DESC LIMIT 1"""
            )
            
            if config:
                config = config[0]
                return {
                    "enabled": bool(config[0]),
                    "global_stop_loss_usd": float(config[1]),
                    "global_take_profit_usd": float(config[2]),
                    "check_interval_seconds": int(config[3]),
                    "last_check_time": config[4]
                }
            else:
                return {
                    "enabled": False,
                    "global_stop_loss_usd": 500.0,
                    "global_take_profit_usd": 1000.0,
                    "check_interval_seconds": 30,
                    "last_check_time": None
                }
                
        except Exception as e:
            logger.error(f"获取组合配置失败: {e}")
            return None
    
    def update_portfolio_config(self, config):
        """更新全局组合配置"""
        try:
            db_manager.execute_query(
                """UPDATE portfolio_sl_tp 
                   SET enabled = ?, global_stop_loss_usd = ?, 
                       global_take_profit_usd = ?, check_interval_seconds = ?,
                       updated_at = CURRENT_TIMESTAMP""",
                (config["enabled"], config["global_stop_loss_usd"],
                 config["global_take_profit_usd"], config["check_interval_seconds"])
            )
            
            logger.info("全局组合配置更新成功")
            return {"success": True, "message": "配置更新成功"}
            
        except Exception as e:
            logger.error(f"更新组合配置失败: {e}")
            return {"success": False, "message": str(e)}

# 创建全局配置管理器实例
config_manager = TradingConfigManager()
