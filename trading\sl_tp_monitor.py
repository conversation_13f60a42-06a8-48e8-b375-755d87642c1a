"""
止盈止损监控模块
"""
import threading
import time
from datetime import datetime
from database.manager import db_manager
from mt5.connection import mt5_connection
from utils.logger import logger


class SlTpMonitor:
    """止盈止损监控器"""
    
    def __init__(self, check_interval=10):
        self.check_interval = check_interval  # 检查间隔（秒）
        self.monitoring = False
        self.monitor_thread = None
        self.lock = threading.Lock()
    
    def get_individual_rules(self):
        """获取个人订单止盈止损规则"""
        try:
            result = db_manager.execute_query(
                """SELECT ticket, profit_threshold, loss_threshold, enabled 
                   FROM individual_floating_pl_rules 
                   WHERE enabled = 1"""
            )
            
            rules = []
            if result:
                for row in result:
                    rules.append({
                        'ticket': row[0],
                        'profit_threshold': row[1],
                        'loss_threshold': row[2],
                        'enabled': bool(row[3])
                    })
            
            return rules
            
        except Exception as e:
            logger.error(f"获取个人止盈止损规则失败: {e}")
            return []
    
    def get_global_rules(self):
        """获取全局止盈止损规则"""
        try:
            result = db_manager.execute_query(
                """SELECT profit_threshold, loss_threshold, enabled 
                   FROM global_sl_tp_rules 
                   WHERE id = 1 AND enabled = 1"""
            )
            
            if result:
                row = result[0]
                return {
                    'profit_threshold': row[0],
                    'loss_threshold': row[1],
                    'enabled': bool(row[2])
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取全局止盈止损规则失败: {e}")
            return None
    
    def get_total_rules(self):
        """获取总浮动盈亏规则"""
        try:
            result = db_manager.execute_query(
                """SELECT profit_threshold, loss_threshold, enabled 
                   FROM total_floating_pl_rules 
                   WHERE id = 1 AND enabled = 1"""
            )
            
            if result:
                row = result[0]
                return {
                    'profit_threshold': row[0],
                    'loss_threshold': row[1],
                    'enabled': bool(row[2])
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取总浮动盈亏规则失败: {e}")
            return None
    
    def close_position(self, ticket, reason="止盈止损触发"):
        """平仓订单"""
        try:
            # 获取持仓信息
            positions = mt5_connection.get_positions()
            position = next((pos for pos in positions if pos.get('ticket') == ticket), None)
            
            if not position:
                logger.warning(f"订单#{ticket}不存在，无法平仓")
                return False
            
            # 执行平仓
            from trading.engine import trading_engine
            result = trading_engine.close_position(ticket, reason)
            
            if result.get('success'):
                logger.info(f"订单#{ticket}平仓成功: {reason}")
                return True
            else:
                logger.error(f"订单#{ticket}平仓失败: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            logger.error(f"平仓订单#{ticket}失败: {e}")
            return False
    
    def close_all_positions(self, reason="总止盈止损触发"):
        """平仓所有订单"""
        try:
            positions = mt5_connection.get_positions()
            if not positions:
                return True
            
            closed_count = 0
            for position in positions:
                ticket = position.get('ticket')
                if self.close_position(ticket, reason):
                    closed_count += 1
            
            logger.info(f"总止盈止损触发，成功平仓{closed_count}个订单")
            return closed_count > 0
            
        except Exception as e:
            logger.error(f"平仓所有订单失败: {e}")
            return False
    
    def check_individual_rules(self, positions):
        """检查个人订单规则"""
        try:
            rules = self.get_individual_rules()
            if not rules:
                return False
            
            closed_count = 0
            
            for rule in rules:
                ticket = rule['ticket']
                profit_threshold = rule['profit_threshold']
                loss_threshold = rule['loss_threshold']
                
                # 找到对应的持仓
                position = next((pos for pos in positions if pos.get('ticket') == ticket), None)
                if not position:
                    continue
                
                current_profit = position.get('profit', 0)
                
                logger.debug(f"检查订单#{ticket}: 当前盈亏={current_profit:.2f}, 止盈={profit_threshold}, 止损={loss_threshold}")
                
                # 检查止盈条件
                if profit_threshold and current_profit >= profit_threshold:
                    logger.info(f"订单#{ticket}达到止盈阈值{profit_threshold}，当前盈亏{current_profit:.2f}")
                    if self.close_position(ticket, f"个人止盈触发(${current_profit:.2f}>=${profit_threshold})"):
                        closed_count += 1
                
                # 检查止损条件
                elif loss_threshold and current_profit <= loss_threshold:
                    logger.info(f"订单#{ticket}达到止损阈值{loss_threshold}，当前盈亏{current_profit:.2f}")
                    if self.close_position(ticket, f"个人止损触发(${current_profit:.2f}<=${loss_threshold})"):
                        closed_count += 1
            
            return closed_count > 0
            
        except Exception as e:
            logger.error(f"检查个人规则失败: {e}")
            return False
    
    def check_global_rules(self, positions):
        """检查全局规则"""
        try:
            rules = self.get_global_rules()
            if not rules:
                return False
            
            profit_threshold = rules['profit_threshold']
            loss_threshold = rules['loss_threshold']
            closed_count = 0
            
            for position in positions:
                ticket = position.get('ticket')
                current_profit = position.get('profit', 0)
                
                logger.debug(f"检查订单#{ticket}全局规则: 当前盈亏={current_profit:.2f}, 止盈={profit_threshold}, 止损={loss_threshold}")
                
                # 检查止盈条件
                if profit_threshold and current_profit >= profit_threshold:
                    logger.info(f"订单#{ticket}达到全局止盈阈值{profit_threshold}，当前盈亏{current_profit:.2f}")
                    if self.close_position(ticket, f"全局止盈触发(${current_profit:.2f}>=${profit_threshold})"):
                        closed_count += 1
                
                # 检查止损条件
                elif loss_threshold and current_profit <= loss_threshold:
                    logger.info(f"订单#{ticket}达到全局止损阈值{loss_threshold}，当前盈亏{current_profit:.2f}")
                    if self.close_position(ticket, f"全局止损触发(${current_profit:.2f}<=${loss_threshold})"):
                        closed_count += 1
            
            return closed_count > 0
            
        except Exception as e:
            logger.error(f"检查全局规则失败: {e}")
            return False
    
    def check_total_rules(self, positions):
        """检查总浮动盈亏规则"""
        try:
            rules = self.get_total_rules()
            if not rules:
                return False
            
            total_profit = sum(pos.get('profit', 0) for pos in positions)
            profit_threshold = rules['profit_threshold']
            loss_threshold = rules['loss_threshold']
            
            logger.debug(f"检查总浮动盈亏: 当前总盈亏={total_profit:.2f}, 止盈={profit_threshold}, 止损={loss_threshold}")
            
            # 检查总止盈条件
            if profit_threshold and total_profit >= profit_threshold:
                logger.info(f"总浮动盈亏达到止盈阈值{profit_threshold}，当前总盈亏{total_profit:.2f}")
                return self.close_all_positions(f"总止盈触发(${total_profit:.2f}>=${profit_threshold})")
            
            # 检查总止损条件
            elif loss_threshold and total_profit <= loss_threshold:
                logger.info(f"总浮动盈亏达到止损阈值{loss_threshold}，当前总盈亏{total_profit:.2f}")
                return self.close_all_positions(f"总止损触发(${total_profit:.2f}<=${loss_threshold})")
            
            return False
            
        except Exception as e:
            logger.error(f"检查总规则失败: {e}")
            return False
    
    def monitor_loop(self):
        """监控循环"""
        logger.info("止盈止损监控线程启动")
        
        while self.monitoring:
            try:
                # 检查MT5连接
                if not mt5_connection.check_connection():
                    logger.debug("MT5未连接，跳过本次监控")
                    time.sleep(self.check_interval)
                    continue
                
                # 获取所有持仓
                positions = mt5_connection.get_positions()
                if not positions:
                    logger.debug("当前无持仓，跳过监控")
                    time.sleep(self.check_interval)
                    continue
                
                logger.debug(f"开始检查{len(positions)}个持仓的止盈止损条件")
                
                # 按优先级检查规则
                # 1. 总浮动盈亏规则（最高优先级）
                if self.check_total_rules(positions):
                    logger.info("总浮动盈亏规则触发，跳过其他检查")
                    time.sleep(self.check_interval)
                    continue
                
                # 2. 全局止盈止损规则
                if self.check_global_rules(positions):
                    logger.info("全局止盈止损规则触发")
                
                # 3. 个人订单规则
                if self.check_individual_rules(positions):
                    logger.info("个人订单规则触发")
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
            
            # 等待下次检查
            time.sleep(self.check_interval)
        
        logger.info("止盈止损监控线程结束")
    
    def start_monitoring(self):
        """启动监控"""
        with self.lock:
            if self.monitoring:
                logger.warning("止盈止损监控已在运行")
                return False
            
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            self.monitor_thread.start()
            logger.info("止盈止损监控已启动")
            return True
    
    def stop_monitoring(self):
        """停止监控"""
        with self.lock:
            if not self.monitoring:
                logger.warning("止盈止损监控未在运行")
                return False
            
            self.monitoring = False
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)
            
            logger.info("止盈止损监控已停止")
            return True
    
    def is_monitoring(self):
        """检查是否正在监控"""
        return self.monitoring and self.monitor_thread and self.monitor_thread.is_alive()
    
    def get_status(self):
        """获取监控状态"""
        return {
            'monitoring': self.monitoring,
            'thread_alive': self.monitor_thread.is_alive() if self.monitor_thread else False,
            'check_interval': self.check_interval
        }


# 全局监控器实例
sl_tp_monitor = SlTpMonitor()
