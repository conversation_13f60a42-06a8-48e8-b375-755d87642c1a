"""
Webhook处理模块 - 接收和处理TradingView警报
"""
import json
from datetime import datetime
from flask import request, jsonify
from database.manager import db_manager
from utils.logger import logger
from utils.helpers import normalize_symbol, normalize_signal_direction, validate_alert_data, generate_alert_id

class WebhookHandler:
    """Webhook处理器"""
    
    def __init__(self):
        self.supported_sources = ['3.pine', 'tradingview']
    
    def receive_tradingview_alert(self):
        """
        接收TradingView警报的主要处理函数
        
        Returns:
            Flask响应对象
        """
        try:
            # 获取请求数据
            if request.is_json:
                alert_data = request.get_json()
            else:
                # 尝试解析文本数据
                try:
                    alert_data = json.loads(request.get_data(as_text=True))
                except json.JSONDecodeError:
                    return jsonify({"success": False, "error": "无效的JSON数据"}), 400
            
            if not alert_data:
                return jsonify({"success": False, "error": "空的请求数据"}), 400
            
            logger.info(f"收到TradingView警报: {alert_data}")
            
            # 处理警报
            result = self.process_alert(alert_data)
            
            if result["success"]:
                return jsonify(result), 200
            else:
                return jsonify(result), 400
                
        except Exception as e:
            logger.error(f"Webhook处理失败: {e}")
            return jsonify({"success": False, "error": str(e)}), 500
    
    def process_alert(self, alert_data):
        """
        处理警报数据
        
        Args:
            alert_data: 原始警报数据
            
        Returns:
            处理结果字典
        """
        try:
            # 1. 数据验证
            is_valid, error_message = validate_alert_data(alert_data)
            if not is_valid:
                return {"success": False, "error": error_message}
            
            # 2. 标准化数据
            processed_alert = self.standardize_alert_data(alert_data)
            
            # 3. 保存警报记录
            alert_id = self.save_alert_record(processed_alert, alert_data)
            processed_alert["alert_id"] = alert_id

            # 4. 执行交易策略
            from trading.engine import trading_strategy_engine
            trade_result = trading_strategy_engine.process_alert(processed_alert)

            # 5. 更新警报处理结果
            process_result = trade_result.get("result_type", "已处理")
            process_reason = trade_result.get("reason", "")
            order_ticket = trade_result.get("order_ticket")

            self.mark_alert_processed(
                alert_id,
                process_result=process_result,
                process_reason=process_reason,
                order_ticket=order_ticket
            )

            # 6. 记录处理日志
            logger.info(f"警报处理完成: {processed_alert['standard_symbol']} {processed_alert['standard_signal']} - {process_result}")

            return {
                "success": True,
                "message": "警报处理成功",
                "alert_id": alert_id,
                "processed_data": processed_alert,
                "trade_result": trade_result
            }
            
        except Exception as e:
            logger.error(f"处理警报失败: {e}")
            return {"success": False, "error": str(e)}
    
    def standardize_alert_data(self, alert_data):
        """
        标准化警报数据
        
        Args:
            alert_data: 原始警报数据
            
        Returns:
            标准化后的警报数据
        """
        # 标准化交易对符号
        original_symbol = alert_data.get("symbol") or alert_data.get("交易对", "")
        standard_symbol = normalize_symbol(original_symbol)

        # 标准化信号方向 - 支持多种字段名
        original_signal = (
            alert_data.get("signal_direction") or
            alert_data.get("事件") or
            alert_data.get("信号") or
            alert_data.get("signal") or
            ""
        )
        standard_signal = normalize_signal_direction(original_signal)
        
        # 构建标准化警报数据
        processed_alert = {
            "alert_type": (
                alert_data.get("alert_type") or
                alert_data.get("指标名称") or
                "综合信号"
            ),
            "original_symbol": original_symbol,
            "standard_symbol": standard_symbol,
            "original_signal": original_signal,
            "standard_signal": standard_signal,
            "timeframe": (
                alert_data.get("timeframe") or
                alert_data.get("周期") or
                "1m"
            ),
            "signal_strength": (
                alert_data.get("signal_strength") or
                alert_data.get("信号强度") or
                "中"
            ),
            "price": float(
                alert_data.get("price") or
                alert_data.get("价格") or
                0.0
            ),
            "timestamp": (
                alert_data.get("timestamp") or
                alert_data.get("时间") or
                datetime.now().isoformat()
            ),
            "source": alert_data.get("source", "tradingview"),
            "processed_time": datetime.now().isoformat(),
            "additional_data": alert_data.get("additional_data", {})
        }
        
        # 记录映射信息
        if original_symbol != standard_symbol:
            logger.info(f"交易对映射: {original_symbol} -> {standard_symbol}")
        
        if original_signal != standard_signal:
            logger.info(f"信号映射: {original_signal} -> {standard_signal}")
        
        return processed_alert
    
    def save_alert_record(self, processed_alert, raw_data):
        """
        保存警报记录到数据库
        
        Args:
            processed_alert: 处理后的警报数据
            raw_data: 原始警报数据
            
        Returns:
            警报ID
        """
        try:
            alert_id = generate_alert_id()
            
            db_manager.execute_query(
                """INSERT INTO alerts (
                    alert_id, original_symbol, standard_symbol, 
                    original_signal, standard_signal, timeframe,
                    signal_strength, price, timestamp, raw_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (
                    alert_id,
                    processed_alert["original_symbol"],
                    processed_alert["standard_symbol"],
                    processed_alert["original_signal"],
                    processed_alert["standard_signal"],
                    processed_alert["timeframe"],
                    processed_alert["signal_strength"],
                    processed_alert["price"],
                    processed_alert["timestamp"],
                    json.dumps(raw_data, ensure_ascii=False)
                )
            )
            
            return alert_id
            
        except Exception as e:
            logger.error(f"保存警报记录失败: {e}")
            raise
    
    def get_alert_history(self, limit=100, offset=0, symbol=None, signal_type=None, time_range=None):
        """
        获取警报历史记录

        Args:
            limit: 限制数量
            offset: 偏移量
            symbol: 交易对过滤
            signal_type: 信号类型过滤
            time_range: 时间范围过滤

        Returns:
            警报历史列表
        """
        try:
            # 构建WHERE条件
            where_conditions = []
            params = []

            if symbol:
                where_conditions.append("standard_symbol = ?")
                params.append(symbol)

            if signal_type:
                where_conditions.append("standard_signal = ?")
                params.append(signal_type)

            if time_range:
                if time_range == '24h':
                    where_conditions.append("created_at >= datetime('now', '-1 day')")
                elif time_range == '3d':
                    where_conditions.append("created_at >= datetime('now', '-3 days')")
                elif time_range == '7d':
                    where_conditions.append("created_at >= datetime('now', '-7 days')")
                elif time_range == '30d':
                    where_conditions.append("created_at >= datetime('now', '-30 days')")

            # 构建SQL查询
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            sql = f"""SELECT alert_id, original_symbol, standard_symbol,
                             original_signal, standard_signal, timeframe,
                             signal_strength, price, timestamp, processed, created_at
                      FROM alerts
                      WHERE {where_clause}
                      ORDER BY created_at DESC
                      LIMIT ? OFFSET ?"""

            params.extend([limit, offset])
            alerts = db_manager.execute_query(sql, params)
            
            alert_list = []
            for alert in alerts:
                alert_list.append({
                    "alert_id": alert[0],
                    "original_symbol": alert[1],
                    "standard_symbol": alert[2],
                    "original_signal": alert[3],
                    "standard_signal": alert[4],
                    "timeframe": alert[5],
                    "signal_strength": alert[6],
                    "price": alert[7],
                    "timestamp": alert[8],
                    "processed": bool(alert[9]),
                    "created_at": alert[10]
                })
            
            return alert_list

        except Exception as e:
            logger.error(f"获取警报历史失败: {e}")
            return []

    def get_alert_count(self, symbol=None, signal_type=None, time_range=None):
        """
        获取警报总数

        Args:
            symbol: 交易对过滤
            signal_type: 信号类型过滤
            time_range: 时间范围过滤

        Returns:
            警报总数
        """
        try:
            # 构建WHERE条件
            where_conditions = []
            params = []

            if symbol:
                where_conditions.append("standard_symbol = ?")
                params.append(symbol)

            if signal_type:
                where_conditions.append("standard_signal = ?")
                params.append(signal_type)

            if time_range:
                if time_range == '24h':
                    where_conditions.append("created_at >= datetime('now', '-1 day')")
                elif time_range == '3d':
                    where_conditions.append("created_at >= datetime('now', '-3 days')")
                elif time_range == '7d':
                    where_conditions.append("created_at >= datetime('now', '-7 days')")
                elif time_range == '30d':
                    where_conditions.append("created_at >= datetime('now', '-30 days')")

            # 构建SQL查询
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            sql = f"SELECT COUNT(*) FROM alerts WHERE {where_clause}"

            result = db_manager.execute_query(sql, params)
            return result[0][0] if result else 0

        except Exception as e:
            logger.error(f"获取警报总数失败: {e}")
            return 0

    def get_alert_raw_data(self, alert_id):
        """
        获取警报原始JSON数据

        Args:
            alert_id: 警报ID

        Returns:
            原始JSON数据
        """
        try:
            result = db_manager.execute_query(
                "SELECT raw_data FROM alerts WHERE alert_id = ?",
                (alert_id,)
            )

            if result:
                raw_data_str = result[0][0]
                if raw_data_str:
                    return json.loads(raw_data_str)

            return None

        except Exception as e:
            logger.error(f"获取警报原始数据失败: {e}")
            return None
    
    def get_alert_statistics(self, days=7):
        """
        获取警报统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            统计信息字典
        """
        try:
            # 总警报数
            total_alerts = db_manager.execute_query(
                "SELECT COUNT(*) FROM alerts WHERE created_at >= datetime('now', '-{} days')".format(days)
            )[0][0]
            
            # 按交易对统计
            symbol_stats = db_manager.execute_query(
                """SELECT standard_symbol, COUNT(*) as count
                   FROM alerts 
                   WHERE created_at >= datetime('now', '-{} days')
                   GROUP BY standard_symbol 
                   ORDER BY count DESC""".format(days)
            )
            
            # 按信号方向统计
            signal_stats = db_manager.execute_query(
                """SELECT standard_signal, COUNT(*) as count
                   FROM alerts 
                   WHERE created_at >= datetime('now', '-{} days')
                   GROUP BY standard_signal""".format(days)
            )
            
            return {
                "total_alerts": total_alerts,
                "symbol_statistics": [{"symbol": row[0], "count": row[1]} for row in symbol_stats],
                "signal_statistics": [{"signal": row[0], "count": row[1]} for row in signal_stats],
                "period_days": days
            }
            
        except Exception as e:
            logger.error(f"获取警报统计失败: {e}")
            return {
                "total_alerts": 0,
                "symbol_statistics": [],
                "signal_statistics": [],
                "period_days": days
            }
    
    def mark_alert_processed(self, alert_id, process_result=None, process_reason=None, order_ticket=None):
        """
        标记警报为已处理

        Args:
            alert_id: 警报ID
            process_result: 处理结果类型
            process_reason: 处理原因详情
            order_ticket: 订单号（如果有）
        """
        try:
            if process_result:
                db_manager.execute_query(
                    """UPDATE alerts SET processed = TRUE, process_result = ?,
                       process_reason = ?, order_ticket = ? WHERE alert_id = ?""",
                    (process_result, process_reason, order_ticket, alert_id)
                )
            else:
                db_manager.execute_query(
                    "UPDATE alerts SET processed = TRUE WHERE alert_id = ?",
                    (alert_id,)
                )
        except Exception as e:
            logger.error(f"标记警报已处理失败: {e}")

    def execute_trading_strategy(self, alert_data):
        """
        执行交易策略

        Args:
            alert_data: 警报数据

        Returns:
            交易执行结果
        """
        try:
            from trading.strategy import trading_strategy
            result = trading_strategy.execute_strategy(alert_data)
            logger.info(f"交易策略执行结果: {result}")
            return result
        except Exception as e:
            logger.error(f"执行交易策略失败: {e}")
            return {"success": False, "error": str(e)}

    def send_notifications(self, alert_data, trading_result):
        """
        发送通知

        Args:
            alert_data: 警报数据
            trading_result: 交易执行结果
        """
        try:
            from trading.config_manager import config_manager
            from notifications.bark import bark_manager

            # 如果启用了警报转发，发送Bark通知
            bark_forward = config_manager.get_system_config("bark_forward_enabled")
            if bark_forward == "True":
                bark_manager.send_alert_forward(alert_data)
                logger.info("Bark警报转发通知已发送")

        except Exception as e:
            logger.error(f"发送通知失败: {e}")

# 创建全局Webhook处理器实例
webhook_handler = WebhookHandler()
