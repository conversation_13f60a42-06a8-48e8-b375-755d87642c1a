/**
 * 页面管理模块
 */

class PageManager {
    constructor() {
        this.currentPage = 'dashboard';
        this.pages = {
            'dashboard': this.renderDashboard,
            'trades': this.renderTrades,
            'positions': this.renderPositions,
            'config': this.renderConfig,
            'alerts': this.renderAlerts,
            'logs': this.renderLogs,
            'settings': this.renderSettings
        };
    }

    /**
     * 显示指定页面
     */
    showPage(pageId) {
        // 隐藏所有页面
        document.querySelectorAll('.page-content').forEach(page => {
            page.style.display = 'none';
        });

        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        const navLink = document.getElementById(`nav-${pageId}`);
        if (navLink) {
            navLink.classList.add('active');
        }

        // 显示目标页面
        const targetPage = document.getElementById(`${pageId}Page`);
        if (targetPage) {
            targetPage.style.display = 'block';
            this.currentPage = pageId;

            // 渲染页面内容
            if (this.pages[pageId]) {
                this.pages[pageId].call(this);
            }
        }
    }

    /**
     * 渲染仪表板页面
     */
    renderDashboard() {
        // 仪表板内容已在HTML中定义，这里只需要刷新数据
        if (ui) {
            ui.loadInitialData();
        }
    }

    /**
     * 渲染交易记录页面
     */
    renderTrades() {
        const page = document.getElementById('tradesPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-list-ul"></i> 交易记录
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="pageManager.exportTrades()">
                            <i class="bi bi-download"></i> 导出
                        </button>
                        <button class="btn btn-outline-secondary" onclick="pageManager.refreshTrades()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选器 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="tradesSymbolFilter">
                                <option value="">所有交易对</option>
                                <option value="XAUUSD">XAUUSD</option>
                                <option value="BTCUSD">BTCUSD</option>
                                <option value="ETHUSD">ETHUSD</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="tradesStatusFilter">
                                <option value="">所有状态</option>
                                <option value="open">持仓中</option>
                                <option value="closed">已平仓</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control form-control-sm" id="tradesDateFilter">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary btn-sm w-100" onclick="pageManager.filterTrades()">
                                <i class="bi bi-funnel"></i> 筛选
                            </button>
                        </div>
                    </div>

                    <!-- 交易记录表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>交易对</th>
                                    <th>方向</th>
                                    <th>手数</th>
                                    <th>开仓价</th>
                                    <th>平仓价</th>
                                    <th>盈亏</th>
                                    <th>开仓时间</th>
                                    <th>平仓时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody id="tradesTableBody">
                                <tr>
                                    <td colspan="10" class="text-center text-muted">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav>
                        <ul class="pagination pagination-sm justify-content-center" id="tradesPagination">
                        </ul>
                    </nav>
                </div>
            </div>
        `;

        this.loadTrades();
    }

    /**
     * 渲染持仓管理页面
     */
    renderPositions() {
        const page = document.getElementById('positionsPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-pie-chart"></i> 持仓管理
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-danger" onclick="pageManager.closeAllPositions()">
                            <i class="bi bi-x-circle"></i> 全部平仓
                        </button>
                        <button class="btn btn-outline-secondary" onclick="pageManager.refreshPositions()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>交易对</th>
                                    <th>方向</th>
                                    <th>手数</th>
                                    <th>开仓价</th>
                                    <th>当前价</th>
                                    <th>浮动盈亏</th>
                                    <th>止损</th>
                                    <th>止盈</th>
                                    <th>持仓时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="positionsDetailTableBody">
                                <tr>
                                    <td colspan="11" class="text-center text-muted">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        this.loadPositions();
    }

    /**
     * 渲染配置管理页面
     */
    renderConfig() {
        const page = document.getElementById('configPage');
        page.innerHTML = `
            <!-- 页面标题 -->
            <div class="row mb-3">
                <div class="col-12">
                    <h3>⚙️ 配置管理</h3>
                    <p class="text-muted">所有配置修改后实时生效，无需重启程序</p>
                </div>
            </div>

            <!-- 配置导航 -->
            <div class="row mb-4">
                <div class="col-12">
                    <ul class="nav nav-pills nav-fill" id="configTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="symbols-tab" data-bs-toggle="pill" href="#symbols-config" role="tab">
                                📊 交易对配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="global-tab" data-bs-toggle="pill" href="#global-config" role="tab">
                                🌐 全局配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="bark-tab" data-bs-toggle="pill" href="#bark-config" role="tab">
                                🔔 通知配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="risk-tab" data-bs-toggle="pill" href="#risk-config" role="tab">
                                🛡️ 风险控制
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 配置内容 -->
            <div class="tab-content" id="configTabContent">
                <!-- 交易对配置 -->
                <div class="tab-pane fade show active" id="symbols-config" role="tabpanel">
                    <div class="row">
                        <div class="col-lg-8 col-md-12 mb-3">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">📊 交易对配置</h5>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-success" onclick="enableAllSymbols()">全部启用</button>
                                        <button class="btn btn-warning" onclick="disableAllSymbols()">全部禁用</button>
                                        <button class="btn btn-info" onclick="resetToDefaults()">恢复默认</button>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive">
                                        <table class="table table-hover mb-0" id="symbolsConfigTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th class="d-none d-md-table-cell" width="80">启用</th>
                                                    <th>交易对</th>
                                                    <th class="d-none d-lg-table-cell">分类</th>
                                                    <th width="80">手数</th>
                                                    <th width="100">止损</th>
                                                    <th width="100">止盈</th>
                                                    <th class="d-none d-md-table-cell" width="80">超时</th>
                                                    <th width="60">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="symbolsConfigBody">
                                                <!-- 动态生成 -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-12">
                            <div class="card">
                                <div class="card-header">⚡ 快速配置</div>
                                <div class="card-body">
                                    <p class="text-muted">批量配置工具开发中...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 其他配置标签页内容 -->
                <div class="tab-pane fade" id="global-config" role="tabpanel">
                    <div class="card">
                        <div class="card-header">🌐 全局配置</div>
                        <div class="card-body">
                            <p class="text-muted">全局配置功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="bark-config" role="tabpanel">
                    <div class="card">
                        <div class="card-header">🔔 通知配置</div>
                        <div class="card-body">
                            <p class="text-muted">通知配置功能开发中...</p>
                        </div>
                    </div>
                </div>

                <div class="tab-pane fade" id="risk-config" role="tabpanel">
                    <div class="card">
                        <div class="card-header">🛡️ 风险控制</div>
                        <div class="card-body">
                            <p class="text-muted">风险控制配置功能开发中...</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 加载配置数据
        if (configManager) {
            configManager.loadAllConfigs();
        }
    }

    /**
     * 渲染警报历史页面
     */
    renderAlerts() {
        const page = document.getElementById('alertsPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-bell"></i> 警报历史
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">警报历史功能开发中...</p>
                </div>
            </div>
        `;
    }

    /**
     * 渲染系统日志页面
     */
    renderLogs() {
        const page = document.getElementById('logsPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-file-text"></i> 系统日志
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">系统日志功能开发中...</p>
                </div>
            </div>
        `;
    }

    /**
     * 渲染系统设置页面
     */
    renderSettings() {
        const page = document.getElementById('settingsPage');
        page.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-sliders"></i> 系统设置
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">系统设置功能开发中...</p>
                </div>
            </div>
        `;
    }

    /**
     * 加载交易记录
     */
    async loadTrades() {
        try {
            const response = await ui.apiCall('/api/trades');
            // 处理交易记录数据
            console.log('交易记录:', response.data);
        } catch (error) {
            console.error('加载交易记录失败:', error);
        }
    }

    /**
     * 加载持仓数据
     */
    async loadPositions() {
        try {
            const response = await ui.apiCall('/api/positions');
            // 处理持仓数据
            console.log('持仓数据:', response.data);
        } catch (error) {
            console.error('加载持仓数据失败:', error);
        }
    }
}

// 全局页面管理器实例
let pageManager;

// 初始化页面管理器
document.addEventListener('DOMContentLoaded', function() {
    pageManager = new PageManager();
});

// 全局函数供HTML调用
function showPage(pageId) {
    if (pageManager) {
        pageManager.showPage(pageId);
    }
}
