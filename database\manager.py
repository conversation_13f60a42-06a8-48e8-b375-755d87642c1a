"""
数据库管理模块
"""
import sqlite3
import threading
from datetime import datetime
from contextlib import contextmanager
from config import Config
from utils.logger import logger

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path=None):
        self.db_path = db_path or Config.DATABASE_PATH
        self._local = threading.local()
        self.init_database()
    
    def get_connection(self):
        """获取数据库连接（线程安全）"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            # 启用外键约束
            self._local.connection.execute("PRAGMA foreign_keys = ON")
        
        return self._local.connection
    
    @contextmanager
    def get_cursor(self):
        """获取数据库游标（上下文管理器）"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
            conn.commit()
        except Exception as e:
            conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            cursor.close()
    
    def execute_query(self, query, params=None):
        """执行查询并返回结果"""
        try:
            with self.get_cursor() as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    return cursor.fetchall()
                else:
                    return cursor.rowcount
        except Exception as e:
            logger.error(f"执行查询失败: {query}, 参数: {params}, 错误: {e}")
            raise
    
    def execute_many(self, query, params_list):
        """批量执行查询"""
        try:
            with self.get_cursor() as cursor:
                cursor.executemany(query, params_list)
                return cursor.rowcount
        except Exception as e:
            logger.error(f"批量执行查询失败: {query}, 错误: {e}")
            raise
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            # 创建管理员用户表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    salt VARCHAR(32) NOT NULL,
                    email VARCHAR(100),
                    last_login DATETIME,
                    login_attempts INTEGER DEFAULT 0,
                    locked_until DATETIME NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建用户会话表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token VARCHAR(255) UNIQUE NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    expires_at DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES admin_users(id)
                )
            """)
            
            # 创建登录日志表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS login_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50),
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    login_result VARCHAR(20),
                    failure_reason VARCHAR(100),
                    login_time DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建警报记录表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alert_id VARCHAR(50) UNIQUE,
                    original_symbol VARCHAR(20),
                    standard_symbol VARCHAR(20),
                    original_signal VARCHAR(50),
                    standard_signal VARCHAR(10),
                    timeframe VARCHAR(10),
                    signal_strength VARCHAR(10),
                    price DECIMAL(10,5),
                    timestamp DATETIME,
                    processed BOOLEAN DEFAULT FALSE,
                    process_result VARCHAR(50),
                    process_reason TEXT,
                    order_ticket INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    raw_data TEXT
                )
            """)

            # 为alerts表添加新字段（如果不存在）
            # 检查字段是否已存在，避免重复添加
            try:
                # 检查process_result字段
                result = self.execute_query("PRAGMA table_info(alerts)")
                existing_columns = [row[1] for row in result] if result else []

                if 'process_result' not in existing_columns:
                    self.execute_query("ALTER TABLE alerts ADD COLUMN process_result VARCHAR(50)")
                    logger.info("已添加process_result字段到alerts表")

                if 'process_reason' not in existing_columns:
                    self.execute_query("ALTER TABLE alerts ADD COLUMN process_reason TEXT")
                    logger.info("已添加process_reason字段到alerts表")

                if 'order_ticket' not in existing_columns:
                    self.execute_query("ALTER TABLE alerts ADD COLUMN order_ticket INTEGER")
                    logger.info("已添加order_ticket字段到alerts表")

            except Exception as e:
                logger.debug(f"检查或添加alerts表字段时出现异常: {e}")
                pass  # 字段可能已存在或其他非关键错误

            # 创建交易记录表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alert_id VARCHAR(50),
                    symbol VARCHAR(20),
                    direction VARCHAR(10),
                    lot_size DECIMAL(10,4),
                    open_price DECIMAL(10,5),
                    close_price DECIMAL(10,5),
                    stop_loss_price DECIMAL(10,5),
                    take_profit_price DECIMAL(10,5),
                    stop_loss_usd DECIMAL(10,2),
                    take_profit_usd DECIMAL(10,2),
                    actual_stop_loss_usd DECIMAL(10,2),
                    actual_take_profit_usd DECIMAL(10,2),
                    status VARCHAR(20),
                    open_time DATETIME,
                    close_time DATETIME,
                    close_reason VARCHAR(100),
                    open_reason VARCHAR(200),
                    profit_usd DECIMAL(10,2),
                    mt5_ticket INTEGER,
                    category VARCHAR(20),
                    magic_number INTEGER,
                    comment TEXT,
                    last_sync_time DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 为现有表添加新字段（如果不存在）
            self._add_column_if_not_exists('trades', 'open_reason', 'VARCHAR(200)')
            self._add_column_if_not_exists('trades', 'last_sync_time', 'DATETIME')
            self._add_column_if_not_exists('trades', 'created_at', 'DATETIME')
            self._add_column_if_not_exists('trades', 'updated_at', 'DATETIME')
            
            # 创建交易对配置表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS symbol_config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20) UNIQUE,
                    enabled BOOLEAN DEFAULT TRUE,
                    lot_size DECIMAL(10,4),
                    stop_loss_usd DECIMAL(10,2),
                    take_profit_usd DECIMAL(10,2),
                    signal_timeout INTEGER DEFAULT 180,
                    category VARCHAR(20),
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建系统配置表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key VARCHAR(50) UNIQUE,
                    value TEXT,
                    description VARCHAR(200),
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建全局组合止盈止损配置表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS portfolio_sl_tp (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    enabled BOOLEAN DEFAULT FALSE,
                    global_stop_loss_usd DECIMAL(10,2),
                    global_take_profit_usd DECIMAL(10,2),
                    check_interval_seconds INTEGER DEFAULT 30,
                    last_check_time DATETIME,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建余额记录表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS balance_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    balance DECIMAL(15,2),
                    equity DECIMAL(15,2),
                    margin DECIMAL(15,2),
                    free_margin DECIMAL(15,2),
                    daily_pnl DECIMAL(10,2),
                    open_positions INTEGER,
                    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建通知记录表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    notification_type VARCHAR(50),
                    title VARCHAR(200),
                    body TEXT,
                    bark_device_1_sent BOOLEAN DEFAULT FALSE,
                    bark_device_2_sent BOOLEAN DEFAULT FALSE,
                    sent_at DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建持仓监控表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS position_monitoring (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ticket INTEGER UNIQUE,
                    symbol VARCHAR(20),
                    direction VARCHAR(10),
                    timeout_seconds INTEGER,
                    last_signal_time DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """)

            # 创建个人订单止盈止损规则表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS individual_floating_pl_rules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ticket INTEGER UNIQUE NOT NULL,
                    profit_threshold DECIMAL(10,2),
                    loss_threshold DECIMAL(10,2),
                    enabled BOOLEAN DEFAULT TRUE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 创建总浮动盈亏规则表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS total_floating_pl_rules (
                    id INTEGER PRIMARY KEY DEFAULT 1,
                    profit_threshold DECIMAL(10,2),
                    loss_threshold DECIMAL(10,2),
                    enabled BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # 创建全局止盈止损规则表
            self.execute_query("""
                CREATE TABLE IF NOT EXISTS global_sl_tp_rules (
                    id INTEGER PRIMARY KEY DEFAULT 1,
                    profit_threshold DECIMAL(10,2),
                    loss_threshold DECIMAL(10,2),
                    enabled BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            logger.info("数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _add_column_if_not_exists(self, table_name, column_name, column_type):
        """安全地添加列（如果不存在）"""
        try:
            # 检查列是否存在
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [row[1] for row in cursor.fetchall()]

            if column_name not in columns:
                # 列不存在，添加它
                if 'DEFAULT CURRENT_TIMESTAMP' in column_type:
                    # SQLite不支持在ALTER TABLE中使用DEFAULT CURRENT_TIMESTAMP
                    # 先添加列，然后更新默认值
                    base_type = column_type.replace(' DEFAULT CURRENT_TIMESTAMP', '')
                    cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {base_type}")
                    conn.commit()
                else:
                    cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")
                    conn.commit()
                logger.info(f"已添加列 {table_name}.{column_name}")
            else:
                logger.debug(f"列 {table_name}.{column_name} 已存在，跳过添加")
        except Exception as e:
            logger.warning(f"添加列 {table_name}.{column_name} 失败: {e}")

    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')

# 创建全局数据库管理器实例
db_manager = DatabaseManager()
