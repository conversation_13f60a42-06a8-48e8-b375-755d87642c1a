"""
MetaTrader5 连接管理模块
"""
import MetaTrader5 as mt5
import time
import threading
from datetime import datetime
from config import Config
from utils.logger import logger

class MT5Connection:
    """MT5连接管理器"""
    
    def __init__(self):
        self.is_connected = False
        self.connection_lock = threading.Lock()
        self.last_connection_check = None
        self.connection_check_interval = 30  # 30秒检查一次连接
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.auto_reconnect_enabled = True
        self.connection_monitor_thread = None
        self.last_successful_connection = None
        self.connection_errors = []
        self.start_connection_monitor()
    
    def connect(self, login=None, password=None, server=None, path=None):
        """
        连接到MT5终端
        
        Args:
            login: MT5账户号
            password: MT5密码
            server: MT5服务器
            path: MT5终端路径
        
        Returns:
            bool: 连接是否成功
        """
        with self.connection_lock:
            try:
                # 优先使用传入的参数，然后是保存的配置文件，最后是环境变量
                mt5_login = login
                mt5_password = password
                mt5_server = server
                mt5_path = path or Config.MT5_PATH

                # 如果没有传入参数，尝试从配置文件读取
                if not all([mt5_login, mt5_password, mt5_server]):
                    try:
                        import json
                        import os

                        config_file = 'config/mt5_config.json'
                        if os.path.exists(config_file):
                            with open(config_file, 'r', encoding='utf-8') as f:
                                saved_config = json.load(f)

                            mt5_login = mt5_login or saved_config.get('login')
                            mt5_password = mt5_password or saved_config.get('password')
                            mt5_server = mt5_server or saved_config.get('server')

                            logger.info("从配置文件加载MT5连接参数")
                    except Exception as e:
                        logger.warning(f"读取MT5配置文件失败: {e}")

                # 最后使用环境变量作为后备
                mt5_login = mt5_login or Config.MT5_LOGIN
                mt5_password = mt5_password or Config.MT5_PASSWORD
                mt5_server = mt5_server or Config.MT5_SERVER
                
                # 初始化MT5连接
                if mt5_path:
                    if not mt5.initialize(path=mt5_path):
                        logger.error(f"MT5初始化失败: {mt5.last_error()}")
                        return False
                else:
                    if not mt5.initialize():
                        logger.error(f"MT5初始化失败: {mt5.last_error()}")
                        return False
                
                # 登录账户
                if mt5_login and mt5_password and mt5_server:
                    if not mt5.login(mt5_login, password=mt5_password, server=mt5_server):
                        logger.error(f"MT5登录失败: {mt5.last_error()}")
                        mt5.shutdown()
                        return False
                
                # 验证连接
                account_info = mt5.account_info()
                if account_info is None:
                    logger.error("无法获取账户信息")
                    mt5.shutdown()
                    return False
                
                self.is_connected = True
                self.last_connection_check = datetime.now()
                
                logger.info(f"MT5连接成功 - 账户: {account_info.login}, 服务器: {account_info.server}")
                logger.info(f"账户余额: ${account_info.balance:.2f}, 净值: ${account_info.equity:.2f}")
                
                return True
                
            except Exception as e:
                logger.error(f"MT5连接异常: {e}")
                self.is_connected = False
                return False
    
    def disconnect(self):
        """断开MT5连接"""
        with self.connection_lock:
            try:
                if self.is_connected:
                    mt5.shutdown()
                    self.is_connected = False
                    logger.info("MT5连接已断开")
            except Exception as e:
                logger.error(f"断开MT5连接时发生错误: {e}")
    
    def check_connection(self):
        """检查连接状态"""
        try:
            # 如果最近检查过连接，直接返回状态
            if (self.last_connection_check and 
                (datetime.now() - self.last_connection_check).seconds < self.connection_check_interval):
                return self.is_connected
            
            # 尝试获取账户信息来验证连接
            account_info = mt5.account_info()
            if account_info is None:
                self.is_connected = False
                logger.warning("MT5连接已断开")
            else:
                self.is_connected = True
            
            self.last_connection_check = datetime.now()
            return self.is_connected
            
        except Exception as e:
            logger.error(f"检查MT5连接状态时发生错误: {e}")
            self.is_connected = False
            return False
    
    def ensure_connection(self):
        """确保MT5连接正常，如果断开则重新连接"""
        if not self.check_connection():
            logger.info("尝试重新连接MT5...")
            return self.connect()
        return True

    def reconnect(self):
        """强制重新连接MT5"""
        try:
            logger.info("开始重新连接MT5...")

            # 先断开现有连接
            self.disconnect()

            # 重新连接
            result = self.connect()

            if result:
                logger.info("MT5重新连接成功")
            else:
                logger.error("MT5重新连接失败")

            return result

        except Exception as e:
            logger.error(f"MT5重连过程中发生错误: {e}")
            return False

    def start_connection_monitor(self):
        """启动连接监控线程"""
        if self.connection_monitor_thread is None or not self.connection_monitor_thread.is_alive():
            self.connection_monitor_thread = threading.Thread(target=self._connection_monitor_loop, daemon=True)
            self.connection_monitor_thread.start()
            logger.info("MT5连接监控线程已启动")

    def _connection_monitor_loop(self):
        """连接监控循环"""
        import time

        while True:
            try:
                time.sleep(self.connection_check_interval)

                if self.auto_reconnect_enabled:
                    current_status = self.check_connection()

                    if not current_status and self.reconnect_attempts < self.max_reconnect_attempts:
                        logger.warning(f"检测到MT5连接断开，尝试自动重连 (第{self.reconnect_attempts + 1}次)")

                        if self.reconnect():
                            logger.info("MT5自动重连成功")
                            self.reconnect_attempts = 0
                            self.last_successful_connection = time.time()
                        else:
                            self.reconnect_attempts += 1
                            logger.error(f"MT5自动重连失败 (第{self.reconnect_attempts}次)")

                            if self.reconnect_attempts >= self.max_reconnect_attempts:
                                logger.error("MT5自动重连达到最大尝试次数，停止自动重连")
                                self.auto_reconnect_enabled = False

                    elif current_status:
                        # 连接正常，重置重连计数
                        if self.reconnect_attempts > 0:
                            logger.info("MT5连接已恢复正常")
                            self.reconnect_attempts = 0
                            self.auto_reconnect_enabled = True
                        self.last_successful_connection = time.time()

            except Exception as e:
                logger.error(f"连接监控线程异常: {e}")
                time.sleep(5)  # 异常时短暂等待

    def get_connection_stats(self):
        """获取连接统计信息"""
        import time

        stats = {
            'is_connected': self.check_connection(),
            'reconnect_attempts': self.reconnect_attempts,
            'max_reconnect_attempts': self.max_reconnect_attempts,
            'auto_reconnect_enabled': self.auto_reconnect_enabled,
            'last_successful_connection': self.last_successful_connection,
            'monitor_thread_alive': self.connection_monitor_thread.is_alive() if self.connection_monitor_thread else False
        }

        if self.last_successful_connection:
            stats['time_since_last_success'] = time.time() - self.last_successful_connection

        return stats

    def reset_reconnect_attempts(self):
        """重置重连尝试计数"""
        self.reconnect_attempts = 0
        self.auto_reconnect_enabled = True
        logger.info("MT5重连尝试计数已重置")

    def get_account_info(self):
        """获取账户信息"""
        try:
            if not self.ensure_connection():
                return None
            
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("无法获取账户信息")
                return None
            
            return {
                'login': account_info.login,
                'server': account_info.server,
                'name': account_info.name,
                'company': account_info.company,
                'currency': account_info.currency,
                'balance': account_info.balance,
                'equity': account_info.equity,
                'margin': account_info.margin,
                'free_margin': account_info.margin_free,
                'margin_level': account_info.margin_level,
                'profit': account_info.profit
            }
            
        except Exception as e:
            logger.error(f"获取账户信息失败: {e}")
            return None
    
    def get_symbol_info(self, symbol):
        """获取交易对信息"""
        try:
            if not self.ensure_connection():
                return None
            
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"无法获取交易对信息: {symbol}")
                return None
            
            return {
                'name': symbol_info.name,
                'description': symbol_info.description,
                'currency_base': symbol_info.currency_base,
                'currency_profit': symbol_info.currency_profit,
                'currency_margin': symbol_info.currency_margin,
                'digits': symbol_info.digits,
                'point': symbol_info.point,
                'spread': symbol_info.spread,
                'volume_min': symbol_info.volume_min,
                'volume_max': symbol_info.volume_max,
                'volume_step': symbol_info.volume_step,
                'contract_size': symbol_info.trade_contract_size,
                'filling_mode': symbol_info.filling_mode,
                'trade_mode': symbol_info.trade_mode
            }
            
        except Exception as e:
            logger.error(f"获取交易对信息失败: {e}")
            return None
    
    def get_tick(self, symbol):
        """获取最新报价"""
        try:
            if not self.ensure_connection():
                return None
            
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                logger.error(f"无法获取报价: {symbol}")
                return None
            
            return {
                'symbol': symbol,
                'time': datetime.fromtimestamp(tick.time),
                'bid': tick.bid,
                'ask': tick.ask,
                'last': tick.last,
                'volume': tick.volume,
                'spread': tick.ask - tick.bid
            }
            
        except Exception as e:
            logger.error(f"获取报价失败: {e}")
            return None
    
    def get_positions(self, symbol=None):
        """获取持仓列表"""
        try:
            if not self.ensure_connection():
                return []
            
            if symbol:
                positions = mt5.positions_get(symbol=symbol)
            else:
                positions = mt5.positions_get()
            
            if positions is None:
                return []
            
            position_list = []
            for pos in positions:
                try:
                    # 确保profit字段不为None
                    profit_value = pos.profit if pos.profit is not None else 0.0

                    position_data = {
                        'ticket': pos.ticket,
                        'symbol': pos.symbol,
                        'type': 0 if pos.type == mt5.POSITION_TYPE_BUY else 1,  # 0=买入, 1=卖出
                        'type_str': 'BUY' if pos.type == mt5.POSITION_TYPE_BUY else 'SELL',
                        'volume': float(pos.volume) if pos.volume is not None else 0.0,
                        'price_open': float(pos.price_open) if pos.price_open is not None else 0.0,
                        'price_current': float(pos.price_current) if pos.price_current is not None else 0.0,
                        'sl': float(pos.sl) if pos.sl is not None else 0.0,
                        'tp': float(pos.tp) if pos.tp is not None else 0.0,
                        'profit': float(profit_value),
                        'swap': float(pos.swap) if pos.swap is not None else 0.0,
                        'comment': pos.comment if pos.comment is not None else '',
                        'magic': int(pos.magic) if pos.magic is not None else 0,
                        'time': datetime.fromtimestamp(pos.time) if pos.time is not None else datetime.now()
                    }

                    position_list.append(position_data)
                    logger.debug(f"处理持仓: {pos.ticket} {pos.symbol} 盈亏:{profit_value}")

                except Exception as e:
                    logger.error(f"处理持仓数据失败 {pos.ticket}: {e}")
                    continue
            
            return position_list

        except Exception as e:
            logger.error(f"获取持仓列表失败: {e}")
            return []

    def close_position(self, ticket, reason="手动平仓"):
        """平仓指定订单"""
        try:
            if not self.ensure_connection():
                logger.error("MT5未连接，无法平仓")
                return False

            # 获取持仓信息
            position = mt5.positions_get(ticket=ticket)
            if not position:
                logger.error(f"未找到订单 {ticket}")
                return False

            position = position[0]
            symbol = position.symbol
            volume = position.volume
            position_type = position.type

            # 获取当前价格
            symbol_info = mt5.symbol_info_tick(symbol)
            if symbol_info is None:
                logger.error(f"无法获取 {symbol} 的价格信息")
                return False

            # 确定平仓价格和类型
            if position_type == mt5.POSITION_TYPE_BUY:
                # 多头持仓，使用卖价平仓
                price = symbol_info.bid
                order_type = mt5.ORDER_TYPE_SELL
            else:
                # 空头持仓，使用买价平仓
                price = symbol_info.ask
                order_type = mt5.ORDER_TYPE_BUY

            # 构建平仓请求
            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": reason,
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            # 发送平仓请求
            result = mt5.order_send(request)

            if result is None:
                logger.error(f"平仓请求失败: {mt5.last_error()}")
                return False

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"平仓失败: {result.retcode} - {result.comment}")
                return False

            logger.info(f"订单 {ticket} 平仓成功，平仓价格: {result.price}")
            return True

        except Exception as e:
            logger.error(f"平仓订单 {ticket} 异常: {e}")
            return False

    def get_orders(self, symbol=None):
        """获取挂单列表"""
        try:
            if not self.ensure_connection():
                return []
            
            if symbol:
                orders = mt5.orders_get(symbol=symbol)
            else:
                orders = mt5.orders_get()
            
            if orders is None:
                return []
            
            order_list = []
            for order in orders:
                order_list.append({
                    'ticket': order.ticket,
                    'symbol': order.symbol,
                    'type': order.type,
                    'volume': order.volume_initial,
                    'price_open': order.price_open,
                    'sl': order.sl,
                    'tp': order.tp,
                    'comment': order.comment,
                    'magic': order.magic,
                    'time_setup': datetime.fromtimestamp(order.time_setup)
                })
            
            return order_list
            
        except Exception as e:
            logger.error(f"获取挂单列表失败: {e}")
            return []

# 创建全局MT5连接实例
mt5_connection = MT5Connection()

# 自动连接函数
def auto_connect():
    """自动连接MT5"""
    return mt5_connection.connect()
