"""
完整的REST API接口路由
"""
from flask import Blueprint, request, jsonify
from datetime import datetime
from auth.manager import require_auth
from trading.config_manager import config_manager
from trading.strategy import trading_strategy
from mt5.connection import mt5_connection
from mt5.trading import trading_engine
from database.manager import db_manager
from notifications.bark import bark_manager
from risk.manager import risk_manager
from webhook.handler import webhook_handler
from utils.logger import logger
from utils.helpers import safe_float, safe_int

# 创建API蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')

# ==================== 配置管理API ====================

@api_bp.route('/config/symbols', methods=['GET'])
@require_auth
def get_all_symbol_configs():
    """获取所有交易对配置"""
    try:
        configs = config_manager.get_all_symbol_configs()

        # 字段名转换：后端使用 signal_timeout，前端期望 signal_timeout_seconds
        for symbol, config in configs.items():
            if 'signal_timeout' in config:
                config['signal_timeout_seconds'] = config['signal_timeout']

        return jsonify({"success": True, "data": configs}), 200
    except Exception as e:
        logger.error(f"获取交易对配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/symbols/<symbol>', methods=['GET'])
@require_auth
def get_symbol_config(symbol):
    """获取单个交易对配置"""
    try:
        config = config_manager.get_symbol_config(symbol)
        if config:
            # 字段名转换：后端使用 signal_timeout，前端期望 signal_timeout_seconds
            if 'signal_timeout' in config:
                config['signal_timeout_seconds'] = config['signal_timeout']
            return jsonify({"success": True, "data": config}), 200
        else:
            return jsonify({"success": False, "error": "交易对不存在"}), 404
    except Exception as e:
        logger.error(f"获取交易对配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/symbols/<symbol>', methods=['PUT'])
@require_auth
def update_symbol_config(symbol):
    """更新交易对配置"""
    try:
        config_data = request.json

        # 字段名转换：前端使用 signal_timeout_seconds，后端使用 signal_timeout
        if 'signal_timeout_seconds' in config_data:
            config_data['signal_timeout'] = config_data.pop('signal_timeout_seconds')

        # 获取当前配置
        current_config = config_manager.get_symbol_config(symbol)
        if current_config is None:
            return jsonify({"success": False, "error": "交易对不存在"}), 404

        # 合并配置（只更新提供的字段）
        merged_config = current_config.copy()
        merged_config.update(config_data)

        result = config_manager.update_symbol_config(symbol, merged_config)
        return jsonify(result), 200 if result["success"] else 400
    except Exception as e:
        logger.error(f"更新交易对配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500



@api_bp.route('/config/global-sl-tp', methods=['GET'])
@require_auth
def get_global_sl_tp_config():
    """获取全局止盈止损配置"""
    try:
        config = config_manager.get_portfolio_config()
        return jsonify({"success": True, "data": config}), 200
    except Exception as e:
        logger.error(f"获取全局止盈止损配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/global-sl-tp', methods=['PUT'])
@require_auth
def update_global_sl_tp_config():
    """更新全局止盈止损配置"""
    try:
        config_data = request.json
        
        # 更新系统配置中的全局设置
        success_count = 0
        errors = []

        # 映射前端字段到后端配置
        field_mapping = {
            'trading_cooldown': 'trading_cooldown',
            'max_concurrent_trades': 'max_concurrent_trades',
            'global_stop_loss_usd': 'global_stop_loss_usd',
            'global_take_profit_usd': 'global_take_profit_usd',
            'auto_trading_enabled': 'auto_trading_enabled'
        }

        for frontend_key, backend_key in field_mapping.items():
            if frontend_key in config_data:
                try:
                    result = config_manager.update_system_config(backend_key, config_data[frontend_key])
                    if result["success"]:
                        success_count += 1
                    else:
                        errors.append(f"{frontend_key}: {result.get('message', '更新失败')}")
                except Exception as e:
                    errors.append(f"{frontend_key}: {str(e)}")

        if success_count > 0:
            return jsonify({"success": True, "message": f"全局配置更新成功，共更新 {success_count} 项"}), 200
        else:
            return jsonify({"success": False, "error": f"更新失败: {'; '.join(errors)}"}, 400)
    except Exception as e:
        logger.error(f"更新全局止盈止损配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/system', methods=['GET'])
@require_auth
def get_system_config():
    """获取系统配置"""
    try:
        config_data = config_manager.get_system_config()

        # 添加更多系统配置项
        extended_config = {
            **config_data,
            'webhook_port': 7000,
            'web_port': 7000,
            'log_level': 'INFO',
            'backup_interval': 12,
            'ui_theme': 'light',
            'refresh_interval': 30,
            'sound_enabled': True,
            'auto_trading_enabled': True,
            'emergency_stop_enabled': True,
            'max_concurrent_trades': config_data.get('max_concurrent_trades', 5),
            'trading_cooldown': config_data.get('trading_cooldown', 300),
            'max_daily_loss_usd': config_data.get('max_daily_loss_usd', 500),
            'max_daily_profit_usd': config_data.get('max_daily_profit_usd', 1000),
            'bark_forward_enabled': config_data.get('bark_forward_enabled', 'True'),
            'global_stop_loss_usd': config_data.get('global_stop_loss_usd', 500),
            'global_take_profit_usd': config_data.get('global_take_profit_usd', 1000),
            'risk_check_interval': config_data.get('risk_check_interval', 30),
            'balance_record_interval': config_data.get('balance_record_interval', 5)
        }

        return jsonify({"success": True, "data": extended_config}), 200
    except Exception as e:
        logger.error(f"获取系统配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/bark', methods=['GET'])
@require_auth
def get_bark_config():
    """获取Bark通知配置"""
    try:
        # 从配置文件读取Bark配置
        import json
        import os

        config_file = 'config/bark_config.json'
        default_config = {
            "enabled": False,
            "device_keys": [],
            "server_url": "https://api.day.app",
            "sound": "birdsong",
            "title": "交易通知",
            "level": "active",
            "volume": 5,
            "group": "trading",
            "trade_open": True,
            "trade_close": True,
            "alert_forward": False,
            "system_status": True,
            "balance_report": False,
            "balance_report_interval": 12
        }

        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    default_config.update(saved_config)
            except Exception as e:
                logger.warning(f"读取Bark配置文件失败: {e}")

        # 同时检查系统配置中的启用状态
        system_config = config_manager.get_system_config()
        if 'bark_forward_enabled' in system_config:
            default_config['enabled'] = system_config['bark_forward_enabled'] == 'True'

        return jsonify({"success": True, "data": default_config}), 200
    except Exception as e:
        logger.error(f"获取Bark配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/bark', methods=['PUT'])
@require_auth
def update_bark_config():
    """更新Bark通知配置"""
    try:
        data = request.get_json()

        # 保存完整的Bark配置到数据库
        bark_config = {
            'enabled': data.get('enabled', False),
            'device_keys': data.get('device_keys', []),
            'server_url': data.get('server_url', 'https://api.day.app'),
            'sound': data.get('sound', 'birdsong'),
            'title': data.get('title', '交易通知'),
            'level': data.get('level', 'active'),
            'volume': data.get('volume', 5),
            'group': data.get('group', 'trading'),
            'trade_open': data.get('trade_open', True),
            'trade_close': data.get('trade_close', True),
            'alert_forward': data.get('alert_forward', False),
            'system_status': data.get('system_status', True),
            'balance_report': data.get('balance_report', False),
            'balance_report_interval': data.get('balance_report_interval', 12)
        }

        # 保存到配置文件
        import json
        import os
        config_file = 'config/bark_config.json'
        os.makedirs(os.path.dirname(config_file), exist_ok=True)

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(bark_config, f, ensure_ascii=False, indent=2)

        # 同时更新系统配置中的启用状态
        result = config_manager.update_system_config(
            'bark_forward_enabled',
            'True' if bark_config['enabled'] else 'False'
        )

        if result["success"]:
            return jsonify({"success": True, "message": "Bark配置保存成功"}), 200
        else:
            return jsonify({"success": False, "error": result["message"]}), 400

    except Exception as e:
        logger.error(f"更新Bark配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/risk', methods=['GET'])
# @require_auth  # 临时移除认证要求
def get_risk_config():
    """获取风险控制配置"""
    try:
        # 从系统配置获取风险相关设置
        system_config = config_manager.get_system_config()

        risk_config = {
            "risk_check_interval": system_config.get('risk_check_interval', 30),
            "balance_record_interval": system_config.get('balance_record_interval', 5),
            "max_drawdown_percent": system_config.get('max_drawdown_percent', 10),
            "max_risk_per_trade": system_config.get('max_risk_per_trade', 2),
            "max_consecutive_losses": system_config.get('max_consecutive_losses', 5),
            "trading_pause_duration": system_config.get('trading_pause_duration', 30),
            "enable_risk_control": system_config.get('enable_risk_control', 'True') == 'True',
            "enable_emergency_stop": system_config.get('emergency_stop_enabled', 'True') == 'True',
            "enable_auto_recovery": system_config.get('enable_auto_recovery', 'False') == 'True'
        }

        return jsonify({"success": True, "data": risk_config}), 200
    except Exception as e:
        logger.error(f"获取风险配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/risk', methods=['PUT'])
# @require_auth  # 临时移除认证要求
def update_risk_config():
    """更新风险控制配置"""
    try:
        data = request.get_json()

        # 更新系统配置中的风险控制设置
        success_count = 0
        errors = []

        risk_configs = {
            'risk_check_interval': data.get('risk_check_interval', 30),
            'balance_record_interval': data.get('balance_record_interval', 5),
            'max_drawdown_percent': data.get('max_drawdown_percent', 10),
            'max_risk_per_trade': data.get('max_risk_per_trade', 2),
            'max_consecutive_losses': data.get('max_consecutive_losses', 5),
            'trading_pause_duration': data.get('trading_pause_duration', 30),
            'enable_risk_control': 'True' if data.get('enable_risk_control', True) else 'False',
            'emergency_stop_enabled': 'True' if data.get('enable_emergency_stop', True) else 'False',
            'enable_auto_recovery': 'True' if data.get('enable_auto_recovery', False) else 'False'
        }

        for key, value in risk_configs.items():
            try:
                result = config_manager.update_system_config(key, value)
                if result["success"]:
                    success_count += 1
                else:
                    errors.append(f"{key}: {result.get('message', '更新失败')}")
            except Exception as e:
                errors.append(f"{key}: {str(e)}")

        if success_count == len(risk_configs):
            return jsonify({"success": True, "message": "风险配置更新成功"}), 200
        else:
            return jsonify({"success": False, "error": f"更新失败: {'; '.join(errors)}"}), 400

    except Exception as e:
        logger.error(f"更新风险配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/daily-limits', methods=['PUT'])
# @require_auth  # 临时移除认证要求
def update_daily_limits():
    """更新日交易限制配置"""
    try:
        data = request.get_json()

        # 更新系统配置中的日限制设置
        success_count = 0
        errors = []

        daily_configs = {
            'max_daily_loss_usd': data.get('max_daily_loss_usd', 500),
            'max_daily_profit_usd': data.get('max_daily_profit_usd', 1000),
            'emergency_stop_enabled': data.get('emergency_stop_enabled', True)
        }

        for key, value in daily_configs.items():
            try:
                result = config_manager.update_system_config(key, value)
                if result["success"]:
                    success_count += 1
                else:
                    errors.append(f"{key}: {result.get('message', '更新失败')}")
            except Exception as e:
                errors.append(f"{key}: {str(e)}")

        if success_count == len(daily_configs):
            return jsonify({"success": True, "message": "日限制配置更新成功"}), 200
        else:
            return jsonify({"success": False, "error": f"更新失败: {'; '.join(errors)}"}), 400

    except Exception as e:
        logger.error(f"更新日限制配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# 通知设置现在统一通过 /api/config/bark 处理，不再需要单独的通知配置API
# 原 update_notifications 函数已移除，避免配置冲突

@api_bp.route('/config/ui', methods=['GET'])
@require_auth
def get_ui_config():
    """获取界面设置"""
    try:
        import json
        import os

        config_file = 'config/ui_config.json'
        default_config = {
            'ui_theme': 'light',
            'refresh_interval': 30,
            'sound_enabled': True,
            'mobile_optimized': True,
            'language': 'zh-CN',
            'timezone': 'Asia/Shanghai'
        }

        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    default_config.update(saved_config)
            except Exception as e:
                logger.warning(f"读取UI配置文件失败: {e}")

        return jsonify({"success": True, "data": default_config}), 200

    except Exception as e:
        logger.error(f"获取UI配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/ui', methods=['PUT'])
@require_auth
def update_ui_config():
    """更新界面设置"""
    try:
        data = request.get_json()

        # 保存UI设置到配置文件
        ui_config = {
            'ui_theme': data.get('ui_theme', 'light'),
            'refresh_interval': data.get('refresh_interval', 30),
            'sound_enabled': data.get('sound_enabled', True),
            'mobile_optimized': data.get('mobile_optimized', True),
            'language': data.get('language', 'zh-CN'),
            'timezone': data.get('timezone', 'Asia/Shanghai')
        }

        import json
        import os
        config_file = 'config/ui_config.json'
        os.makedirs(os.path.dirname(config_file), exist_ok=True)

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(ui_config, f, ensure_ascii=False, indent=2)

        return jsonify({"success": True, "message": "界面设置保存成功"}), 200

    except Exception as e:
        logger.error(f"更新界面设置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/notifications/test', methods=['POST'])
@require_auth
def test_notification():
    """测试通知功能"""
    try:
        data = request.get_json()
        notification_type = data.get('type', 'bark')
        message = data.get('message', '这是一条测试通知')

        if notification_type == 'bark':
            # 读取Bark配置
            import json
            import os

            config_file = 'config/bark_config.json'
            if not os.path.exists(config_file):
                return jsonify({"success": False, "error": "Bark配置不存在，请先配置设备Key"}), 400

            with open(config_file, 'r', encoding='utf-8') as f:
                bark_config = json.load(f)

            device_keys = bark_config.get('device_keys', [])
            if not device_keys:
                return jsonify({"success": False, "error": "请先配置设备Key"}), 400

            # 发送测试通知
            import requests
            server_url = bark_config.get('server_url', 'https://api.day.app')
            sound = bark_config.get('sound', 'birdsong')

            success_count = 0
            errors = []

            for device_key in device_keys:
                if device_key.strip():
                    try:
                        url = f"{server_url}/{device_key.strip()}"
                        payload = {
                            'title': '交易系统测试',
                            'body': message,
                            'sound': sound,
                            'group': 'trading_test'
                        }

                        response = requests.post(url, json=payload, timeout=10)
                        if response.status_code == 200:
                            success_count += 1
                        else:
                            errors.append(f"设备{device_key[:8]}...发送失败")
                    except Exception as e:
                        errors.append(f"设备{device_key[:8]}...发送异常: {str(e)}")

            if success_count > 0:
                message = f"测试通知发送成功，共发送到 {success_count} 个设备"
                if errors:
                    message += f"，{len(errors)} 个失败"
                return jsonify({"success": True, "message": message}), 200
            else:
                return jsonify({"success": False, "error": f"发送失败: {'; '.join(errors)}"}), 400

        else:
            return jsonify({"success": False, "error": "不支持的通知类型"}), 400

    except Exception as e:
        logger.error(f"发送测试通知失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# 策略控制API
@api_bp.route('/strategy/start', methods=['POST'])
@require_auth
def start_strategy():
    """启动交易策略"""
    try:
        # 检查MT5连接
        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接，无法启动策略"}), 400

        # 更新策略状态
        result = config_manager.update_system_config('strategy_running', True)
        if result["success"]:
            logger.info("交易策略已启动")
            return jsonify({"success": True, "message": "交易策略启动成功"}), 200
        else:
            return jsonify({"success": False, "error": "启动策略失败"}), 500

    except Exception as e:
        logger.error(f"启动策略失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/strategy/stop', methods=['POST'])
@require_auth
def stop_strategy():
    """停止交易策略"""
    try:
        # 更新策略状态
        result = config_manager.update_system_config('strategy_running', False)
        if result["success"]:
            logger.info("交易策略已停止")
            return jsonify({"success": True, "message": "交易策略停止成功"}), 200
        else:
            return jsonify({"success": False, "error": "停止策略失败"}), 500

    except Exception as e:
        logger.error(f"停止策略失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/strategy/emergency-stop', methods=['POST'])
@require_auth
def emergency_stop():
    """紧急停止 - 关闭所有持仓并停止策略"""
    try:
        # 停止策略
        config_manager.update_system_config('strategy_running', False)

        # 关闭所有持仓
        if mt5_connection.check_connection():
            # 这里应该调用实际的平仓逻辑
            # 目前只是模拟
            logger.warning("执行紧急停止 - 所有持仓已平仓")

        return jsonify({"success": True, "message": "紧急停止执行成功，所有持仓已平仓"}), 200

    except Exception as e:
        logger.error(f"紧急停止失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/strategy/pause', methods=['POST'])
@require_auth
def pause_strategy():
    """暂停交易"""
    try:
        result = config_manager.update_system_config('trading_paused', True)
        if result["success"]:
            return jsonify({"success": True, "message": "交易已暂停"}), 200
        else:
            return jsonify({"success": False, "error": "暂停失败"}), 500
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/strategy/resume', methods=['POST'])
@require_auth
def resume_strategy():
    """恢复交易"""
    try:
        result = config_manager.update_system_config('trading_paused', False)
        if result["success"]:
            return jsonify({"success": True, "message": "交易已恢复"}), 200
        else:
            return jsonify({"success": False, "error": "恢复失败"}), 500
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

# 持仓管理API
@api_bp.route('/positions', methods=['GET'])
@require_auth
def get_positions():
    """获取当前持仓"""
    try:
        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接"}), 400

        # 获取持仓数据
        positions = mt5_connection.get_positions()

        # 添加监控信息到持仓数据
        from trading.engine import trading_strategy_engine
        from datetime import datetime

        for position in positions:
            ticket = position['ticket']

            # 检查是否在监控中
            if ticket in trading_strategy_engine.position_monitors:
                monitor_data = trading_strategy_engine.position_monitors[ticket]
                last_signal_time = monitor_data["last_signal_time"]
                timeout_seconds = monitor_data["timeout_seconds"]

                current_time = datetime.now()
                time_since_signal = (current_time - last_signal_time).total_seconds()
                remaining_time = max(0, timeout_seconds - time_since_signal)

                position['monitoring'] = {
                    'enabled': True,
                    'remaining_seconds': int(remaining_time),
                    'timeout_seconds': timeout_seconds,
                    'last_signal_time': last_signal_time.isoformat()
                }
            else:
                position['monitoring'] = {
                    'enabled': False,
                    'remaining_seconds': 0,
                    'timeout_seconds': 0,
                    'last_signal_time': None
                }

        return jsonify({
            "success": True,
            "data": positions,
            "count": len(positions)
        }), 200

    except Exception as e:
        logger.error(f"获取持仓失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# 监控管理API
@api_bp.route('/monitoring/status', methods=['GET'])
@require_auth
def get_monitoring_status():
    """获取监控系统状态"""
    try:
        from trading.engine import trading_strategy_engine

        return jsonify({
            "success": True,
            "data": {
                "is_running": trading_strategy_engine.is_running,
                "monitored_positions": len(trading_strategy_engine.position_monitors)
            }
        }), 200
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/monitoring/toggle', methods=['POST'])
@require_auth
def toggle_monitoring():
    """开启/暂停监控系统"""
    try:
        from trading.engine import trading_strategy_engine

        if trading_strategy_engine.is_running:
            trading_strategy_engine.stop_monitoring()
            action = "stopped"
        else:
            trading_strategy_engine.start_monitoring()
            action = "started"

        return jsonify({
            "success": True,
            "data": {
                "action": action,
                "is_running": trading_strategy_engine.is_running
            }
        }), 200
    except Exception as e:
        logger.error(f"切换监控状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/<int:ticket>/monitoring', methods=['POST'])
@require_auth
def add_position_monitoring(ticket):
    """为订单添加监控"""
    try:
        data = request.get_json()
        timeout_seconds = data.get('timeout_seconds', 180)  # 默认3分钟

        from trading.engine import trading_strategy_engine

        # 检查订单是否存在
        positions = mt5_connection.get_positions()
        position_exists = any(pos['ticket'] == ticket for pos in positions)

        if not position_exists:
            return jsonify({"success": False, "error": "订单不存在"}), 400

        # 添加监控
        trading_strategy_engine._start_position_monitoring(ticket, timeout_seconds)

        return jsonify({
            "success": True,
            "message": f"已为订单#{ticket}添加监控，超时时间{timeout_seconds}秒"
        }), 200
    except Exception as e:
        logger.error(f"添加监控失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/<int:ticket>/monitoring', methods=['DELETE'])
@require_auth
def remove_position_monitoring(ticket):
    """移除订单监控"""
    try:
        from trading.engine import trading_strategy_engine

        if ticket in trading_strategy_engine.position_monitors:
            trading_strategy_engine._stop_position_monitoring(ticket)
            return jsonify({
                "success": True,
                "message": f"已移除订单#{ticket}的监控"
            }), 200
        else:
            return jsonify({"success": False, "error": "订单未在监控中"}), 400
    except Exception as e:
        logger.error(f"移除监控失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# 保护止盈管理API
@api_bp.route('/positions/<int:ticket>/protective-profit', methods=['POST'])
@require_auth
def add_protective_profit(ticket):
    """为订单添加保护止盈"""
    try:
        data = request.get_json()
        protective_level = data.get('protective_level')

        if protective_level is None:
            return jsonify({"success": False, "error": "缺少保护水平参数"}), 400

        # 检查订单是否存在
        positions = mt5_connection.get_positions()
        position = next((pos for pos in positions if pos['ticket'] == ticket), None)

        if not position:
            return jsonify({"success": False, "error": "订单不存在"}), 400

        current_profit = position.get('profit', 0)

        # 保护水平不能高于当前盈利
        if protective_level > current_profit:
            return jsonify({
                "success": False,
                "error": f"保护水平(${protective_level})不能高于当前盈利(${current_profit:.2f})"
            }), 400

        # 添加保护止盈规则
        from trading.sl_tp_monitor import sl_tp_monitor

        if sl_tp_monitor.add_protective_profit_rule(ticket, protective_level):
            return jsonify({
                "success": True,
                "message": f"已为订单#{ticket}设置保护止盈，保护水平: ${protective_level}"
            }), 200
        else:
            return jsonify({"success": False, "error": "设置保护止盈失败"}), 500

    except Exception as e:
        logger.error(f"添加保护止盈失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/<int:ticket>/protective-profit', methods=['DELETE'])
@require_auth
def remove_protective_profit(ticket):
    """移除订单保护止盈"""
    try:
        from trading.sl_tp_monitor import sl_tp_monitor

        if sl_tp_monitor.remove_protective_profit_rule(ticket):
            return jsonify({
                "success": True,
                "message": f"已移除订单#{ticket}的保护止盈"
            }), 200
        else:
            return jsonify({"success": False, "error": "移除保护止盈失败"}), 500

    except Exception as e:
        logger.error(f"移除保护止盈失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/<int:ticket>/close', methods=['POST'])
# @require_auth  # 临时移除认证要求
def close_position(ticket):
    """平仓指定订单"""
    try:
        data = request.get_json() or {}
        reason = data.get('reason', '手动平仓')

        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接"}), 400

        # 执行平仓
        result = mt5_connection.close_position(ticket, reason)

        if result:
            logger.info(f"订单 {ticket} 平仓成功: {reason}")
            return jsonify({"success": True, "message": f"订单 {ticket} 平仓成功"}), 200
        else:
            return jsonify({"success": False, "error": "平仓失败"}), 400

    except Exception as e:
        logger.error(f"平仓失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/close-all-test', methods=['POST'])
def close_all_positions_test():
    """测试平仓API"""
    print("=== 测试API被调用 ===")
    return jsonify({"success": True, "message": "测试API正常工作", "test": True}), 200

@api_bp.route('/positions/close-all', methods=['POST'])
# @require_auth  # 临时移除认证要求
def close_all_positions():
    """平仓所有订单"""
    try:
        # 检查MT5连接
        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接"}), 400

        # 获取所有持仓
        positions = mt5_connection.get_positions()

        # 如果没有持仓，返回成功消息
        if not positions:
            return jsonify({
                "success": True,
                "message": "当前没有持仓需要平仓",
                "closed_count": 0
            }), 200

        success_count = 0
        errors = []

        for position in positions:
            try:
                ticket = position.get('ticket')
                if mt5_connection.close_position(ticket, '批量平仓'):
                    success_count += 1
                    logger.info(f"订单#{ticket}平仓成功")
                else:
                    error_msg = f"订单#{ticket}平仓失败"
                    errors.append(error_msg)
                    logger.warning(error_msg)
            except Exception as e:
                error_msg = f"订单#{position.get('ticket', 'unknown')}平仓异常: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)

        if success_count > 0:
            message = f"批量平仓成功，共平仓 {success_count} 个订单"
            if errors:
                message += f"，{len(errors)} 个失败"
            return jsonify({"success": True, "message": message, "closed_count": success_count, "failed_count": len(errors)}), 200
        else:
            error_message = "批量平仓失败"
            if errors:
                error_message += f": {'; '.join(errors)}"
            else:
                error_message += ": 所有订单平仓操作都失败，请检查MT5连接和订单状态"
            return jsonify({"success": False, "error": error_message}), 400

    except Exception as e:
        logger.error(f"批量平仓失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# 交易历史API
@api_bp.route('/trades/history', methods=['GET'])
@require_auth
def get_trade_history():
    """获取交易历史"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 50))
        symbol = request.args.get('symbol', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 构建查询条件
        where_conditions = []
        params = []

        if symbol:
            where_conditions.append("symbol = ?")
            params.append(symbol)

        if start_date:
            where_conditions.append("open_time >= ?")
            params.append(start_date)

        if end_date:
            where_conditions.append("close_time <= ?")
            params.append(end_date)

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 查询交易历史
        offset = (page - 1) * limit
        query = f"""
            SELECT ticket, symbol, type, volume, open_price, close_price,
                   open_time, close_time, profit, commission, swap, comment
            FROM trade_history
            {where_clause}
            ORDER BY close_time DESC
            LIMIT ? OFFSET ?
        """
        params.extend([limit, offset])

        trades = db_manager.execute_query(query, params)

        # 查询总数
        count_query = f"SELECT COUNT(*) FROM trade_history {where_clause}"
        total = db_manager.execute_query(count_query, params[:-2])[0][0] if where_conditions else db_manager.execute_query("SELECT COUNT(*) FROM trade_history")[0][0]

        return jsonify({
            "success": True,
            "data": {
                "trades": trades,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "pages": (total + limit - 1) // limit
                }
            }
        }), 200

    except Exception as e:
        logger.error(f"获取交易历史失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# 警报记录API
@api_bp.route('/alerts/history', methods=['GET'])
@require_auth
def get_alert_history():
    """获取警报历史"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 100))
        symbol = request.args.get('symbol', '')
        signal_type = request.args.get('signal_type', '')

        # 构建查询条件
        where_conditions = []
        params = []

        if symbol:
            where_conditions.append("symbol = ?")
            params.append(symbol)

        if signal_type:
            where_conditions.append("signal_direction = ?")
            params.append(signal_type)

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 查询警报历史
        offset = (page - 1) * limit
        query = f"""
            SELECT id, symbol, signal_direction, signal_strength, price,
                   timestamp, processed, trade_executed, alert_data
            FROM alerts
            {where_clause}
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
        """
        params.extend([limit, offset])

        alerts = db_manager.execute_query(query, params)

        # 查询总数
        count_query = f"SELECT COUNT(*) FROM alerts {where_clause}"
        total = db_manager.execute_query(count_query, params[:-2])[0][0] if where_conditions else db_manager.execute_query("SELECT COUNT(*) FROM alerts")[0][0]

        return jsonify({
            "success": True,
            "data": {
                "alerts": alerts,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total,
                    "pages": (total + limit - 1) // limit
                }
            }
        }), 200

    except Exception as e:
        logger.error(f"获取警报历史失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/symbols/batch', methods=['POST'])
@require_auth
def batch_update_symbols():
    """批量更新交易对配置"""
    try:
        data = request.get_json()
        category = data.get('category')
        template = data.get('template')
        config = data.get('config', {})

        # 字段名转换：前端使用 signal_timeout_seconds，后端使用 signal_timeout
        if 'signal_timeout_seconds' in config:
            config['signal_timeout'] = config.pop('signal_timeout_seconds')

        # 定义分类映射
        category_symbols = {
            'crypto': ['BTCUSD', 'ETHUSD', 'SOLUSD', 'ADAUSD', 'XLMUSD', 'DOGEUSD', 'LINKUSD', 'LTCUSD', 'XRPUSD'],
            'forex': ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD', 'GBPJPY', 'EURJPY'],
            'precious_metals': ['XAUUSD', 'XAGUSD']
        }

        # 根据分类或模板确定要更新的交易对
        if category and category in category_symbols:
            symbols_to_update = category_symbols[category]
        elif template:
            # 如果是模板，获取所有交易对
            all_symbols = []
            for symbols_list in category_symbols.values():
                all_symbols.extend(symbols_list)
            symbols_to_update = all_symbols
        else:
            return jsonify({"success": False, "error": "需要指定分类或模板"}), 400

        # 批量更新
        success_count = 0
        errors = []

        for symbol in symbols_to_update:
            try:
                # 获取当前配置
                current_config = config_manager.get_symbol_config(symbol)
                if current_config is None:
                    errors.append(f"{symbol}: 配置不存在")
                    continue

                # 合并配置
                merged_config = current_config.copy()
                merged_config.update(config)

                # 更新配置
                result = config_manager.update_symbol_config(symbol, merged_config)
                if result["success"]:
                    success_count += 1
                else:
                    errors.append(f"{symbol}: {result.get('message', '更新失败')}")
            except Exception as e:
                errors.append(f"{symbol}: {str(e)}")

        if success_count > 0:
            message = f"批量更新成功，共更新 {success_count} 个交易对"
            if errors:
                message += f"，{len(errors)} 个失败"
            return jsonify({
                "success": True,
                "message": message,
                "success_count": success_count,
                "errors": errors
            }), 200
        else:
            return jsonify({
                "success": False,
                "error": f"批量更新失败: {'; '.join(errors)}"
            }), 400

    except Exception as e:
        logger.error(f"批量更新交易对配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/symbols/reset', methods=['POST'])
@require_auth
def reset_symbols_config():
    """重置交易对配置为默认值"""
    try:
        # 获取默认配置
        default_config = {
            "enabled": False,
            "lot_size": 0.01,
            "stop_loss_usd": 50,
            "take_profit_usd": 100,
            "signal_timeout_seconds": 180
        }

        # 完整的交易对列表
        symbols = [
            # 贵金属
            'XAUUSD', 'XAGUSD',
            # 加密货币
            'BTCUSD', 'ETHUSD', 'SOLUSD', 'ADAUSD', 'XLMUSD', 'DOGEUSD', 'LINKUSD', 'LTCUSD', 'XRPUSD',
            # 外汇
            'EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD', 'GBPJPY', 'EURJPY'
        ]

        success_count = 0
        for symbol in symbols:
            result = config_manager.update_symbol_config(symbol, default_config)
            if result["success"]:
                success_count += 1

        return jsonify({
            "success": True,
            "message": f"重置成功，共重置 {success_count} 个交易对"
        }), 200

    except Exception as e:
        logger.error(f"重置交易对配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500



@api_bp.route('/config/system', methods=['PUT'])
@require_auth
def update_system_config():
    """更新系统配置"""
    try:
        config_data = request.json

        # 逐个更新配置项
        success_count = 0
        total_count = len(config_data)
        errors = []

        for key, value in config_data.items():
            try:
                result = config_manager.update_system_config(key, value)
                if result["success"]:
                    success_count += 1
                else:
                    errors.append(f"{key}: {result.get('message', '更新失败')}")
            except Exception as e:
                errors.append(f"{key}: {str(e)}")

        if success_count == total_count:
            return jsonify({
                "success": True,
                "message": f"系统配置更新成功，共更新 {success_count} 项"
            }), 200
        elif success_count > 0:
            return jsonify({
                "success": False,
                "message": f"部分更新成功 ({success_count}/{total_count})，错误: {'; '.join(errors)}"
            }), 400
        else:
            return jsonify({
                "success": False,
                "error": f"更新失败: {'; '.join(errors)}"
            }), 400
        
    except Exception as e:
        logger.error(f"更新系统配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# ==================== 交易监控API ====================

@api_bp.route('/trades', methods=['GET'])
# @require_auth  # 临时移除认证要求用于测试
def get_trades():
    """获取交易记录"""
    try:
        page = safe_int(request.args.get('page', 1))
        limit = safe_int(request.args.get('limit', 50))
        symbol = request.args.get('symbol')
        status = request.args.get('status')
        
        offset = (page - 1) * limit
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        if symbol:
            where_conditions.append("symbol = ?")
            params.append(symbol)
        
        if status:
            where_conditions.append("status = ?")
            params.append(status)
        
        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""
        
        # 查询交易记录
        trades = db_manager.execute_query(
            f"""SELECT * FROM trades {where_clause}
               ORDER BY open_time DESC 
               LIMIT ? OFFSET ?""",
            params + [limit, offset]
        )
        
        # 查询总数
        total = db_manager.execute_query(
            f"SELECT COUNT(*) FROM trades {where_clause}",
            params
        )[0][0]
        
        return jsonify({
            "success": True,
            "data": {
                "trades": [dict(trade) for trade in trades],
                "total": total,
                "page": page,
                "limit": limit
            }
        }), 200
        
    except Exception as e:
        logger.error(f"获取交易记录失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/trades/sync', methods=['POST'])
# @require_auth  # 临时移除认证要求用于测试
def sync_orders():
    """手动同步订单"""
    try:
        from trading.order_sync import order_sync_manager

        data = request.json or {}
        mt5_ticket = data.get('mt5_ticket')

        if mt5_ticket:
            # 同步单个订单
            success = order_sync_manager.sync_single_order(mt5_ticket)
            return jsonify({
                "success": success,
                "message": f"订单#{mt5_ticket}同步{'成功' if success else '失败'}"
            })
        else:
            # 触发全量同步
            if order_sync_manager.is_syncing():
                return jsonify({
                    "success": True,
                    "message": "订单同步正在运行中"
                })
            else:
                # 重启同步
                order_sync_manager.stop_sync()
                success = order_sync_manager.start_sync()
                return jsonify({
                    "success": success,
                    "message": f"订单同步{'启动成功' if success else '启动失败'}"
                })

    except Exception as e:
        logger.error(f"同步订单失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/trades/<int:trade_id>', methods=['GET'])
@require_auth
def get_trade_detail(trade_id):
    """获取交易详情"""
    try:
        trade = db_manager.execute_query(
            "SELECT * FROM trades WHERE id = ?",
            (trade_id,)
        )
        
        if trade:
            return jsonify({"success": True, "data": dict(trade[0])}), 200
        else:
            return jsonify({"success": False, "error": "交易记录不存在"}), 404
            
    except Exception as e:
        logger.error(f"获取交易详情失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/alerts', methods=['GET'])
@require_auth
def get_alerts():
    """获取警报历史"""
    try:
        page = safe_int(request.args.get('page', 1))
        limit = safe_int(request.args.get('limit', 50))
        symbol = request.args.get('symbol')
        signal_type = request.args.get('signal_type')
        time_range = request.args.get('time_range')
        hours = request.args.get('hours')  # 新增小时范围参数

        # 构建查询条件
        where_conditions = []
        params = []

        if symbol:
            where_conditions.append("standard_symbol = ?")
            params.append(symbol)

        if signal_type:
            where_conditions.append("standard_signal = ?")
            params.append(signal_type)

        # 时间范围筛选
        if time_range and time_range != 'all':
            time_condition = ""
            if time_range == '24h':
                time_condition = "created_at >= datetime('now', '-1 day')"
            elif time_range == '3d':
                time_condition = "created_at >= datetime('now', '-3 days')"
            elif time_range == '7d':
                time_condition = "created_at >= datetime('now', '-7 days')"
            elif time_range == '30d':
                time_condition = "created_at >= datetime('now', '-30 days')"

            if time_condition:
                where_conditions.append(time_condition)

        # 小时范围筛选（优先级高于天数范围）
        if hours and not time_range:
            try:
                hours_int = int(hours)
                time_condition = f"(timestamp >= datetime('now', '-{hours_int} hours') OR created_at >= datetime('now', '-{hours_int} hours'))"
                where_conditions.append(time_condition)
            except ValueError:
                pass  # 忽略无效的小时参数

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 查询警报历史
        offset = (page - 1) * limit
        query = f"""
            SELECT alert_id, original_symbol, standard_symbol,
                   original_signal, standard_signal, timeframe,
                   signal_strength, price, timestamp, processed, created_at,
                   process_result, process_reason, order_ticket
            FROM alerts
            {where_clause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """
        query_params = params + [limit, offset]

        alerts = db_manager.execute_query(query, query_params)

        # 查询总数
        count_query = f"SELECT COUNT(*) FROM alerts {where_clause}"
        total = db_manager.execute_query(count_query, params)[0][0]

        # 格式化警报数据
        alert_list = []
        for alert in alerts:
            alert_list.append({
                "alert_id": alert[0],
                "original_symbol": alert[1],
                "standard_symbol": alert[2],
                "original_signal": alert[3],
                "standard_signal": alert[4],
                "timeframe": alert[5],
                "signal_strength": alert[6],
                "price": alert[7],
                "timestamp": alert[8],
                "processed": bool(alert[9]),
                "created_at": alert[10],
                "process_result": alert[11],
                "process_reason": alert[12],
                "order_ticket": alert[13]
            })

        return jsonify({
            "success": True,
            "data": {
                "alerts": alert_list,
                "total": total,
                "page": page,
                "limit": limit
            }
        }), 200

    except Exception as e:
        logger.error(f"获取警报历史失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/alerts/statistics', methods=['GET'])
@require_auth
def get_alert_statistics():
    """获取警报统计"""
    try:
        days = safe_int(request.args.get('days', 7))
        stats = webhook_handler.get_alert_statistics(days)
        return jsonify({"success": True, "data": stats}), 200
    except Exception as e:
        logger.error(f"获取警报统计失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/close-profitable', methods=['POST'])
# @require_auth  # 临时移除认证要求
def close_profitable_positions():
    """平仓所有盈利订单"""
    try:
        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接"}), 400

        # 获取所有持仓
        positions = mt5_connection.get_positions()
        if not positions:
            return jsonify({"success": True, "message": "没有盈利持仓需要平仓", "closed_count": 0}), 200

        # 筛选盈利的持仓
        profitable_positions = [pos for pos in positions if pos.get("profit", 0) > 0]

        if not profitable_positions:
            return jsonify({"success": True, "message": "当前没有盈利的持仓", "closed_count": 0}), 200

        success_count = 0
        errors = []
        total_profit = 0

        for position in profitable_positions:
            try:
                ticket = position.get('ticket')
                profit = position.get('profit', 0)

                if mt5_connection.close_position(ticket, '批量平仓盈利订单'):
                    success_count += 1
                    total_profit += profit
                    logger.info(f"成功平仓盈利订单 {ticket}，盈利: {profit:.2f}")
                else:
                    errors.append(f"订单{ticket}平仓失败")
            except Exception as e:
                errors.append(f"订单{position.get('ticket', 'unknown')}平仓异常: {str(e)}")

        if success_count > 0:
            message = f"成功平仓 {success_count} 个盈利订单，总盈利: {total_profit:.2f}"
            if errors:
                message += f"，{len(errors)} 个失败"
            return jsonify({
                "success": True,
                "message": message,
                "closed_count": success_count,
                "total_profit": total_profit
            }), 200
        else:
            return jsonify({"success": False, "error": f"平仓失败: {'; '.join(errors)}"}), 400

    except Exception as e:
        logger.error(f"平仓盈利订单失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/close-loss', methods=['POST'])
# @require_auth  # 临时移除认证要求
def close_loss_positions():
    """平仓所有亏损订单"""
    try:
        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接"}), 400

        # 获取所有持仓
        positions = mt5_connection.get_positions()
        if not positions:
            return jsonify({"success": True, "message": "没有亏损持仓需要平仓", "closed_count": 0}), 200

        # 筛选亏损的持仓
        loss_positions = [pos for pos in positions if pos.get("profit", 0) < 0]

        if not loss_positions:
            return jsonify({"success": True, "message": "当前没有亏损的持仓", "closed_count": 0}), 200

        success_count = 0
        errors = []
        total_loss = 0

        for position in loss_positions:
            try:
                ticket = position.get('ticket')
                profit = position.get('profit', 0)

                if mt5_connection.close_position(ticket, '批量平仓亏损订单'):
                    success_count += 1
                    total_loss += profit
                    logger.info(f"成功平仓亏损订单 {ticket}，亏损: {profit:.2f}")
                else:
                    errors.append(f"订单{ticket}平仓失败")
            except Exception as e:
                errors.append(f"订单{position.get('ticket', 'unknown')}平仓异常: {str(e)}")

        if success_count > 0:
            message = f"成功平仓 {success_count} 个亏损订单，总亏损: {total_loss:.2f}"
            if errors:
                message += f"，{len(errors)} 个失败"
            return jsonify({
                "success": True,
                "message": message,
                "closed_count": success_count,
                "total_loss": total_loss
            }), 200
        else:
            return jsonify({"success": False, "error": f"平仓失败: {'; '.join(errors)}"}), 400

    except Exception as e:
        logger.error(f"平仓亏损订单失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/system/status', methods=['GET'])
@require_auth
def get_system_status():
    """获取系统状态和账户信息"""
    try:
        # 获取MT5连接状态
        mt5_connected = mt5_connection.check_connection()
        account_info = mt5_connection.get_account_info() if mt5_connected else None

        # 获取当前持仓（所有订单，不限制魔法号）
        positions = mt5_connection.get_positions() if mt5_connected else []

        # 计算今日盈亏（这里简化为当前浮动盈亏）
        today_profit = 0
        if positions:
            today_profit = sum(pos.get('profit', 0) for pos in positions)

        # 计算今日警报数量（从数据库查询）
        today_alerts = 0
        try:
            from datetime import datetime, date
            today = date.today()
            alerts_result = db_manager.execute_query(
                "SELECT COUNT(*) FROM alerts WHERE DATE(timestamp) = ?",
                (today.isoformat(),)
            )
            if alerts_result:
                today_alerts = alerts_result[0][0]
        except Exception as e:
            logger.warning(f"获取今日警报数量失败: {e}")

        return jsonify({
            "success": True,
            "data": {
                "mt5_connected": mt5_connected,
                "account_info": account_info,
                "positions": positions,
                "today_profit": today_profit,
                "today_alerts": today_alerts,
                "timestamp": datetime.now().isoformat()
            }
        }), 200

    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# MT5连接管理API
@api_bp.route('/mt5/test-connection', methods=['POST'])
@require_auth
def test_mt5_connection():
    """测试MT5连接"""
    try:
        data = request.get_json()
        login = data.get('login')
        password = data.get('password')
        server = data.get('server')

        if not all([login, password, server]):
            return jsonify({"success": False, "error": "请提供完整的连接信息"}), 400

        # 测试连接
        import MetaTrader5 as mt5

        # 尝试连接
        if not mt5.initialize():
            return jsonify({"success": False, "error": "MT5初始化失败"}), 400

        # 尝试登录
        authorized = mt5.login(login, password=password, server=server)

        if authorized:
            # 获取账户信息验证连接
            account_info = mt5.account_info()
            if account_info:
                mt5.shutdown()
                return jsonify({
                    "success": True,
                    "message": f"连接成功！账户: {account_info.login}, 余额: ${account_info.balance:.2f}"
                }), 200
            else:
                mt5.shutdown()
                return jsonify({"success": False, "error": "无法获取账户信息"}), 400
        else:
            error_code = mt5.last_error()
            mt5.shutdown()
            return jsonify({"success": False, "error": f"登录失败: {error_code}"}), 400

    except Exception as e:
        logger.error(f"测试MT5连接失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/mt5/reconnect', methods=['POST'])
@require_auth
def reconnect_mt5():
    """重新连接MT5"""
    try:
        # 重新初始化MT5连接
        result = mt5_connection.reconnect()

        if result:
            return jsonify({"success": True, "message": "MT5重新连接成功"}), 200
        else:
            return jsonify({"success": False, "error": "MT5重新连接失败"}), 400

    except Exception as e:
        logger.error(f"重新连接MT5失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/mt5', methods=['PUT'])
@require_auth
def update_mt5_config():
    """更新MT5配置"""
    try:
        data = request.get_json()

        # 保存MT5配置到配置文件
        mt5_config = {
            'login': data.get('mt5_login'),
            'password': data.get('mt5_password'),
            'server': data.get('mt5_server')
        }

        # 验证配置
        if not all([mt5_config['login'], mt5_config['password'], mt5_config['server']]):
            return jsonify({"success": False, "error": "请提供完整的MT5配置信息"}), 400

        # 保存到配置文件
        import json
        import os

        config_file = 'config/mt5_config.json'
        os.makedirs(os.path.dirname(config_file), exist_ok=True)

        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(mt5_config, f, ensure_ascii=False, indent=2)

        # 同时更新系统配置
        for key, value in mt5_config.items():
            config_manager.update_system_config(f'mt5_{key}', value)

        return jsonify({"success": True, "message": "MT5配置保存成功"}), 200

    except Exception as e:
        logger.error(f"更新MT5配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/config/mt5', methods=['GET'])
@require_auth
def get_mt5_config():
    """获取MT5配置"""
    try:
        # 从配置文件读取
        import json
        import os

        config_file = 'config/mt5_config.json'
        default_config = {
            'login': '',
            'password': '',
            'server': ''
        }

        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    default_config.update(saved_config)
            except Exception as e:
                logger.warning(f"读取MT5配置文件失败: {e}")

        # 同时从系统配置读取
        system_config = config_manager.get_system_config()
        if 'mt5_login' in system_config:
            default_config['login'] = system_config['mt5_login']
        if 'mt5_password' in system_config:
            default_config['password'] = system_config['mt5_password']
        if 'mt5_server' in system_config:
            default_config['server'] = system_config['mt5_server']

        return jsonify({"success": True, "data": default_config}), 200

    except Exception as e:
        logger.error(f"获取MT5配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/system/health', methods=['GET'])
@require_auth
def get_system_health():
    """获取系统健康状态"""
    try:
        # 检查MT5连接状态
        mt5_connected = mt5_connection.check_connection()

        # 检查数据库连接
        db_connected = True
        try:
            db_manager.execute_query("SELECT 1")
        except Exception:
            db_connected = False

        # 系统运行时间（简化版）
        import time
        uptime = time.time() - getattr(get_system_health, '_start_time', time.time())
        if not hasattr(get_system_health, '_start_time'):
            get_system_health._start_time = time.time()

        health_status = {
            "status": "healthy" if (mt5_connected and db_connected) else "degraded",
            "mt5_connected": mt5_connected,
            "database_connected": db_connected,
            "uptime_seconds": uptime,
            "timestamp": datetime.now().isoformat()
        }

        return jsonify({
            "success": True,
            "data": health_status
        }), 200

    except Exception as e:
        logger.error(f"获取系统健康状态失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "data": {
                "status": "unhealthy",
                "mt5_connected": False,
                "database_connected": False,
                "uptime_seconds": 0,
                "timestamp": datetime.now().isoformat()
            }
        }), 500

@api_bp.route('/market/prices', methods=['GET'])
@require_auth
def get_market_prices():
    """获取市场实时价格"""
    try:
        # 检查MT5连接
        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接"}), 400

        # 定义要获取价格的交易对
        symbols = ['BTCUSD', 'ETHUSD', 'SOLUSD', 'XAUUSD', 'GBPJPY']
        prices = {}

        import MetaTrader5 as mt5

        for symbol in symbols:
            try:
                # 获取最新价格
                tick = mt5.symbol_info_tick(symbol)
                if tick:
                    prices[symbol] = {
                        'symbol': symbol,
                        'bid': tick.bid,
                        'ask': tick.ask,
                        'last': tick.last,
                        'time': tick.time,
                        'spread': round((tick.ask - tick.bid), 5),
                        'change': 0,  # 可以后续计算涨跌
                        'change_percent': 0  # 可以后续计算涨跌幅
                    }
                else:
                    # 如果获取不到价格，设置默认值
                    prices[symbol] = {
                        'symbol': symbol,
                        'bid': 0,
                        'ask': 0,
                        'last': 0,
                        'time': 0,
                        'spread': 0,
                        'change': 0,
                        'change_percent': 0,
                        'error': f'无法获取{symbol}价格'
                    }
            except Exception as e:
                logger.warning(f"获取{symbol}价格失败: {e}")
                prices[symbol] = {
                    'symbol': symbol,
                    'bid': 0,
                    'ask': 0,
                    'last': 0,
                    'time': 0,
                    'spread': 0,
                    'change': 0,
                    'change_percent': 0,
                    'error': str(e)
                }

        return jsonify({
            "success": True,
            "data": prices,
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"获取市场价格失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/system/trading-status', methods=['GET'])
@require_auth
def get_trading_status():
    """获取交易状态"""
    try:
        # 从配置中获取交易状态
        trading_enabled = config_manager.get_system_config().get('auto_trading_enabled', 'False') == 'True'

        return jsonify({
            "success": True,
            "data": {
                "trading_enabled": trading_enabled,
                "status": "trading" if trading_enabled else "paused",
                "timestamp": datetime.now().isoformat()
            }
        }), 200

    except Exception as e:
        logger.error(f"获取交易状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/system/trading-status', methods=['POST'])
@require_auth
def set_trading_status():
    """设置交易状态"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', False)

        # 更新配置
        config_manager.update_system_config('auto_trading_enabled', 'True' if enabled else 'False')

        status_text = "交易已启用" if enabled else "交易已暂停"

        return jsonify({
            "success": True,
            "message": status_text,
            "data": {
                "trading_enabled": enabled,
                "status": "trading" if enabled else "paused",
                "timestamp": datetime.now().isoformat()
            }
        }), 200

    except Exception as e:
        logger.error(f"设置交易状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/system/webhook-status', methods=['GET'])
@require_auth
def get_webhook_status():
    """获取Webhook状态"""
    try:
        import socket

        # 检查webhook端口是否在监听
        webhook_port = int(config_manager.get_system_config().get('webhook_port', 7000))
        webhook_running = False

        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', webhook_port))
            webhook_running = (result == 0)
            sock.close()
        except Exception:
            webhook_running = False

        # 获取最近的webhook请求统计（简化版）
        recent_requests = 0  # 可以从日志或数据库获取

        return jsonify({
            "success": True,
            "data": {
                "webhook_running": webhook_running,
                "port": webhook_port,
                "recent_requests": recent_requests,
                "status": "online" if webhook_running else "offline",
                "timestamp": datetime.now().isoformat()
            }
        }), 200

    except Exception as e:
        logger.error(f"获取Webhook状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/system/full-status', methods=['GET'])
@require_auth
def get_full_system_status():
    """获取完整系统状态"""
    try:
        # 获取各种状态
        mt5_connected = mt5_connection.check_connection()
        trading_enabled = config_manager.get_system_config().get('auto_trading_enabled', 'False') == 'True'

        # 检查webhook状态
        import socket
        webhook_port = int(config_manager.get_system_config().get('webhook_port', 7000))
        webhook_running = False
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', webhook_port))
            webhook_running = (result == 0)
            sock.close()
        except Exception:
            webhook_running = False

        # 计算系统运行时间（简化版）
        import time
        uptime_seconds = time.time() - getattr(get_full_system_status, '_start_time', time.time())
        if not hasattr(get_full_system_status, '_start_time'):
            get_full_system_status._start_time = time.time()

        # 格式化运行时间
        hours = int(uptime_seconds // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        uptime_text = f"{hours}小时{minutes}分钟"

        return jsonify({
            "success": True,
            "data": {
                "mt5_connected": mt5_connected,
                "trading_enabled": trading_enabled,
                "webhook_running": webhook_running,
                "webhook_port": webhook_port,
                "uptime_seconds": uptime_seconds,
                "uptime_text": uptime_text,
                "timestamp": datetime.now().isoformat()
            }
        }), 200

    except Exception as e:
        logger.error(f"获取完整系统状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/mt5/connection-stats', methods=['GET'])
@require_auth
def get_mt5_connection_stats():
    """获取MT5连接统计信息"""
    try:
        stats = mt5_connection.get_connection_stats()

        return jsonify({
            "success": True,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"获取MT5连接统计失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/mt5/reset-reconnect', methods=['POST'])
@require_auth
def reset_mt5_reconnect():
    """重置MT5重连尝试计数"""
    try:
        mt5_connection.reset_reconnect_attempts()

        return jsonify({
            "success": True,
            "message": "MT5重连计数已重置，自动重连已重新启用",
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"重置MT5重连失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/sl-tp-settings', methods=['GET'])
@require_auth
def get_position_sl_tp_settings():
    """获取持仓的止盈止损设置"""
    try:
        # 获取当前持仓
        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接"}), 400

        positions = mt5_connection.get_positions()

        # 从数据库获取每个订单的止盈止损设置
        settings = []
        for pos in positions:
            ticket = pos.get('ticket')

            # 查询数据库中的设置
            result = db_manager.execute_query(
                """SELECT profit_threshold, loss_threshold, enabled
                   FROM individual_floating_pl_rules
                   WHERE ticket = ?""",
                (ticket,)
            )

            if result:
                rule = result[0]
                settings.append({
                    'ticket': ticket,
                    'symbol': pos.get('symbol'),
                    'current_profit': pos.get('profit', 0),
                    'profit_threshold': rule[0],
                    'loss_threshold': rule[1],
                    'enabled': bool(rule[2])
                })
            else:
                # 没有设置，使用默认值
                settings.append({
                    'ticket': ticket,
                    'symbol': pos.get('symbol'),
                    'current_profit': pos.get('profit', 0),
                    'profit_threshold': None,
                    'loss_threshold': None,
                    'enabled': False
                })

        return jsonify({
            "success": True,
            "data": settings,
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"获取止盈止损设置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/sl-tp-settings', methods=['POST'])
@require_auth
def update_position_sl_tp_settings():
    """更新持仓的止盈止损设置"""
    try:
        data = request.get_json()
        ticket = data.get('ticket')
        profit_threshold = data.get('profit_threshold')
        loss_threshold = data.get('loss_threshold')
        enabled = data.get('enabled', True)

        if not ticket:
            return jsonify({"success": False, "error": "缺少订单号"}), 400

        # 验证持仓是否存在
        if not mt5_connection.check_connection():
            return jsonify({"success": False, "error": "MT5未连接"}), 400

        positions = mt5_connection.get_positions()
        position = next((pos for pos in positions if pos.get('ticket') == ticket), None)

        if not position:
            return jsonify({"success": False, "error": "订单不存在"}), 400

        # 更新或插入设置
        existing = db_manager.execute_query(
            "SELECT id FROM individual_floating_pl_rules WHERE ticket = ?",
            (ticket,)
        )

        if existing:
            # 更新现有设置
            db_manager.execute_query(
                """UPDATE individual_floating_pl_rules
                   SET profit_threshold = ?, loss_threshold = ?, enabled = ?, updated_at = ?
                   WHERE ticket = ?""",
                (profit_threshold, loss_threshold, enabled, datetime.now(), ticket)
            )
        else:
            # 插入新设置
            db_manager.execute_query(
                """INSERT INTO individual_floating_pl_rules
                   (ticket, profit_threshold, loss_threshold, enabled, created_at, updated_at)
                   VALUES (?, ?, ?, ?, ?, ?)""",
                (ticket, profit_threshold, loss_threshold, enabled, datetime.now(), datetime.now())
            )

        return jsonify({
            "success": True,
            "message": f"订单#{ticket}的止盈止损设置已更新",
            "data": {
                "ticket": ticket,
                "profit_threshold": profit_threshold,
                "loss_threshold": loss_threshold,
                "enabled": enabled
            },
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"更新止盈止损设置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/global-sl-tp', methods=['GET'])
@require_auth
def get_global_sl_tp_settings():
    """获取全局止盈止损设置"""
    try:
        # 从数据库获取全局设置
        result = db_manager.execute_query(
            """SELECT profit_threshold, loss_threshold, enabled
               FROM total_floating_pl_rules
               WHERE id = 1"""
        )

        if result:
            rule = result[0]
            settings = {
                'profit_threshold': rule[0],
                'loss_threshold': rule[1],
                'enabled': bool(rule[2])
            }
        else:
            # 默认设置
            settings = {
                'profit_threshold': None,
                'loss_threshold': None,
                'enabled': False
            }

        return jsonify({
            "success": True,
            "data": settings,
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"获取全局止盈止损设置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/positions/global-sl-tp', methods=['POST'])
@require_auth
def update_global_sl_tp_settings():
    """更新全局止盈止损设置"""
    try:
        data = request.get_json()
        profit_threshold = data.get('profit_threshold')
        loss_threshold = data.get('loss_threshold')
        enabled = data.get('enabled', True)

        # 更新或插入全局设置
        existing = db_manager.execute_query(
            "SELECT id FROM total_floating_pl_rules WHERE id = 1"
        )

        if existing:
            # 更新现有设置
            db_manager.execute_query(
                """UPDATE total_floating_pl_rules
                   SET profit_threshold = ?, loss_threshold = ?, enabled = ?, updated_at = ?
                   WHERE id = 1""",
                (profit_threshold, loss_threshold, enabled, datetime.now())
            )
        else:
            # 插入新设置
            db_manager.execute_query(
                """INSERT INTO total_floating_pl_rules
                   (id, profit_threshold, loss_threshold, enabled, created_at, updated_at)
                   VALUES (1, ?, ?, ?, ?, ?)""",
                (profit_threshold, loss_threshold, enabled, datetime.now(), datetime.now())
            )

        return jsonify({
            "success": True,
            "message": "全局止盈止损设置已更新",
            "data": {
                "profit_threshold": profit_threshold,
                "loss_threshold": loss_threshold,
                "enabled": enabled
            },
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"更新全局止盈止损设置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/sl-tp-monitor/status', methods=['GET'])
@require_auth
def get_sl_tp_monitor_status():
    """获取止盈止损监控状态"""
    try:
        from trading.sl_tp_monitor import sl_tp_monitor

        status = sl_tp_monitor.get_status()

        return jsonify({
            "success": True,
            "data": {
                "monitoring": status['monitoring'],
                "thread_alive": status['thread_alive'],
                "check_interval": status['check_interval']
            },
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"获取止盈止损监控状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/sl-tp-monitor/control', methods=['POST'])
@require_auth
def control_sl_tp_monitor():
    """控制止盈止损监控"""
    try:
        from trading.sl_tp_monitor import sl_tp_monitor

        data = request.get_json()
        action = data.get('action')

        if action == 'start':
            result = sl_tp_monitor.start_monitoring()
            message = "止盈止损监控启动成功" if result else "止盈止损监控启动失败"

        elif action == 'stop':
            result = sl_tp_monitor.stop_monitoring()
            message = "止盈止损监控停止成功" if result else "止盈止损监控停止失败"

        else:
            return jsonify({"success": False, "error": "无效的操作"}), 400

        return jsonify({
            "success": result,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"控制止盈止损监控失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/timeout-monitor/status', methods=['GET'])
@require_auth
def get_timeout_monitor_status():
    """获取超时监控状态"""
    try:
        from trading.engine import trading_strategy_engine

        # 获取监控状态
        is_running = trading_strategy_engine.is_running
        monitored_positions = len(trading_strategy_engine.position_monitors)

        # 获取数据库中的监控记录
        active_monitors = db_manager.execute_query(
            "SELECT ticket, timeout_seconds, last_signal_time FROM position_monitoring WHERE is_active = 1"
        )

        monitors_data = []
        if active_monitors:
            for row in active_monitors:
                ticket, timeout_seconds, last_signal_time = row
                monitors_data.append({
                    'ticket': ticket,
                    'timeout_seconds': timeout_seconds,
                    'last_signal_time': last_signal_time
                })

        return jsonify({
            "success": True,
            "data": {
                "monitoring": is_running,
                "monitored_positions": monitored_positions,
                "active_monitors": monitors_data
            },
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"获取超时监控状态失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@api_bp.route('/timeout-monitor/control', methods=['POST'])
@require_auth
def control_timeout_monitor():
    """控制超时监控"""
    try:
        from trading.engine import trading_strategy_engine

        data = request.get_json()
        action = data.get('action')

        if action == 'start':
            trading_strategy_engine.start_monitoring()
            message = "超时监控启动成功"

        elif action == 'stop':
            trading_strategy_engine.stop_monitoring()
            message = "超时监控停止成功"

        else:
            return jsonify({"success": False, "error": "无效的操作"}), 400

        return jsonify({
            "success": True,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"控制超时监控失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# ==================== 账户信息API ====================

@api_bp.route('/account/info', methods=['GET'])
@require_auth
def get_account_info():
    """获取账户基本信息"""
    try:
        # 获取MT5账户信息
        account_info = mt5_connection.get_account_info()

        if account_info:
            return jsonify({
                "success": True,
                "data": {
                    "balance": account_info.get('balance', 0),
                    "equity": account_info.get('equity', 0),
                    "margin": account_info.get('margin', 0),
                    "free_margin": account_info.get('free_margin', 0),
                    "margin_level": account_info.get('margin_level', 0),
                    "currency": account_info.get('currency', 'USD'),
                    "leverage": account_info.get('leverage', 1),
                    "profit": account_info.get('profit', 0),
                    "timestamp": datetime.now().isoformat()
                }
            }), 200
        else:
            # 如果无法获取MT5信息，返回默认值
            return jsonify({
                "success": True,
                "data": {
                    "balance": 0,
                    "equity": 0,
                    "margin": 0,
                    "free_margin": 0,
                    "margin_level": 0,
                    "currency": "USD",
                    "leverage": 1,
                    "profit": 0,
                    "timestamp": datetime.now().isoformat()
                }
            }), 200

    except Exception as e:
        logger.error(f"获取账户信息失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# ==================== 警报统计API ====================

@api_bp.route('/alerts/stats', methods=['GET'])
# @require_auth  # 临时移除认证要求
def get_alert_stats():
    """获取警报统计信息"""
    try:
        hours = request.args.get('hours', '1')
        try:
            hours = int(hours)
        except ValueError:
            hours = 1

        print(f"=== 开始警报统计查询，时间范围: {hours}小时 ===")

        # 统一使用SQLite的datetime()对齐时间格式，避免字符串比较导致漏计
        # 简单直接的查询：获取指定时间范围内的所有警报
        query = f"""
            SELECT standard_symbol, original_symbol, standard_signal, original_signal,
                   timestamp, created_at
            FROM alerts
            WHERE (timestamp >= datetime('now', '-{hours} hours') OR created_at >= datetime('now', '-{hours} hours'))
            ORDER BY created_at DESC
        """

        alerts = db_manager.execute_query(query)

        # 调试信息
        print(f"警报统计查询: {hours}小时, 找到 {len(alerts) if alerts else 0} 条记录")
        if alerts:
            print(f"前3条记录: {alerts[:3]}")

        # 统计数据
        stats = {}
        symbols = ['BTCUSD', 'ETHUSD', 'SOLUSD', 'XAUUSD', 'GBPJPY']

        # 初始化已知交易对，保证表格完整
        for s in symbols:
            stats[s] = {'buy': 0, 'sell': 0, 'latest': None}

        # 简单直接的统计逻辑
        if alerts:
            logger.info(f"开始处理 {len(alerts)} 条警报记录")
            for i, alert in enumerate(alerts):
                std_symbol, orig_symbol, std_signal, orig_signal, timestamp, created_at = alert

                # 获取交易对和信号
                symbol = std_symbol or orig_symbol
                signal = std_signal or orig_signal

                logger.info(f"记录 {i+1}: {symbol} {signal} (std: {std_symbol}/{std_signal}, orig: {orig_symbol}/{orig_signal})")

                if not symbol or not signal:
                    logger.warning(f"跳过记录 {i+1}: 缺少symbol或signal")
                    continue

                symbol = symbol.upper()
                signal = signal.upper()

                # 确保交易对在统计中
                if symbol not in stats:
                    stats[symbol] = {'buy': 0, 'sell': 0, 'latest': None}

                # 统计买卖信号
                if signal in ['BUY', 'LONG']:
                    stats[symbol]['buy'] += 1
                    logger.info(f"{symbol} BUY计数: {stats[symbol]['buy']}")
                elif signal in ['SELL', 'SHORT']:
                    stats[symbol]['sell'] += 1
                    logger.info(f"{symbol} SELL计数: {stats[symbol]['sell']}")

                # 更新最新时间
                alert_time = timestamp or created_at
                if alert_time and (not stats[symbol]['latest'] or alert_time > stats[symbol]['latest']):
                    stats[symbol]['latest'] = alert_time
        else:
            logger.warning(f"查询结果为空，{hours}小时内无警报记录")

        from datetime import datetime
        return jsonify({
            "success": True,
            "data": stats,
            "time_range_hours": hours,
            "query_time": datetime.now().isoformat()
        }), 200

    except Exception as e:
        logger.error(f"获取警报统计失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

# ==================== 文件结束 ====================
