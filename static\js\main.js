/**
 * TradingView自动交易系统 - 前端主要JavaScript文件
 */

class TradingSystemUI {
    constructor() {
        this.sessionToken = this.getSessionToken();
        this.refreshInterval = null;
        this.init();
    }

    init() {
        this.checkAuth();
        this.startAutoRefresh();
        this.bindEvents();
        this.loadInitialData();
    }

    getSessionToken() {
        return localStorage.getItem('session_token') || sessionStorage.getItem('session_token');
    }

    async checkAuth() {
        if (!this.sessionToken) {
            window.location.href = '/login';
            return;
        }

        try {
            const response = await this.apiCall('/api/auth/validate', 'GET');
            if (response.valid) {
                document.getElementById('username').textContent = response.user.username;
            } else {
                this.logout();
            }
        } catch (error) {
            console.error('认证检查失败:', error);
            this.logout();
        }
    }

    async apiCall(url, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Authorization': `Bearer ${this.sessionToken}`,
                'Content-Type': 'application/json'
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);
        
        if (response.status === 401) {
            this.logout();
            return;
        }

        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.error || '请求失败');
        }

        return result;
    }

    async loadInitialData() {
        await this.loadSystemStatus();
        await this.loadPositions();
        await this.loadTrades();
        await this.loadAlerts();
        await this.loadSymbolConfigs();
    }

    async loadSystemStatus() {
        try {
            const response = await this.apiCall('/api/system/status');
            if (!response.success) {
                throw new Error(response.error || '获取系统状态失败');
            }
            const data = response.data;

            // 更新账户信息
            if (data.account_info) {
                document.getElementById('accountBalance').textContent = 
                    this.formatCurrency(data.account_info.balance);
                
                const totalPnL = data.account_info.profit || 0;
                const pnlElement = document.getElementById('totalPnL');
                pnlElement.textContent = this.formatCurrency(totalPnL);
                pnlElement.className = totalPnL >= 0 ? 'metric-value profit-positive' : 'metric-value profit-negative';
            }

            // 更新持仓数量
            document.getElementById('totalPositions').textContent = data.positions.length;

            // 更新系统状态
            const statusIndicator = document.getElementById('systemStatus');
            const statusText = document.getElementById('systemStatusText');

            if (data.mt5_connected) {
                if (data.strategy_status && data.strategy_status.status === 'running') {
                    statusIndicator.className = 'status-indicator status-online';
                    statusText.textContent = '在线运行';
                } else {
                    statusIndicator.className = 'status-indicator status-warning';
                    statusText.textContent = 'MT5已连接';
                }
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = 'MT5离线';
            }

        } catch (error) {
            console.error('加载系统状态失败:', error);
            this.showAlert('danger', '加载系统状态失败: ' + error.message);
        }
    }

    async loadPositions() {
        try {
            const response = await this.apiCall('/api/positions');
            const positions = response.data;

            const tbody = document.querySelector('#positionsTable tbody');
            
            if (positions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center">暂无持仓</td></tr>';
                return;
            }

            tbody.innerHTML = positions.map(pos => `
                <tr>
                    <td>${pos.ticket}</td>
                    <td>${pos.symbol}</td>
                    <td><span class="badge ${pos.type === 'BUY' ? 'bg-success' : 'bg-danger'}">${pos.type === 'BUY' ? '做多' : '做空'}</span></td>
                    <td>${pos.volume}</td>
                    <td>${pos.price_open}</td>
                    <td>${pos.price_current}</td>
                    <td class="${pos.profit >= 0 ? 'profit-positive' : 'profit-negative'}">${this.formatCurrency(pos.profit)}</td>
                    <td>
                        <button class="btn btn-sm btn-danger" onclick="closePosition(${pos.ticket})">
                            <i class="bi bi-x-circle"></i> 平仓
                        </button>
                    </td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('加载持仓失败:', error);
            document.querySelector('#positionsTable tbody').innerHTML = 
                '<tr><td colspan="8" class="text-center text-danger">加载失败</td></tr>';
        }
    }

    async loadTrades() {
        try {
            const response = await this.apiCall('/api/trades?limit=50');
            const trades = response.data.trades;

            const tbody = document.querySelector('#tradesTable tbody');
            
            if (trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center">暂无交易记录</td></tr>';
                return;
            }

            tbody.innerHTML = trades.map(trade => `
                <tr>
                    <td>${trade.symbol}</td>
                    <td><span class="badge ${trade.direction === 'BUY' ? 'bg-success' : 'bg-danger'}">${trade.direction === 'BUY' ? '做多' : '做空'}</span></td>
                    <td>${trade.lot_size}</td>
                    <td>${trade.open_price}</td>
                    <td>${trade.close_price || 'N/A'}</td>
                    <td class="${(trade.profit_usd || 0) >= 0 ? 'profit-positive' : 'profit-negative'}">${this.formatCurrency(trade.profit_usd || 0)}</td>
                    <td>${this.formatDateTime(trade.open_time)}</td>
                    <td>${trade.close_time ? this.formatDateTime(trade.close_time) : 'N/A'}</td>
                </tr>
            `).join('');

        } catch (error) {
            console.error('加载交易记录失败:', error);
            document.querySelector('#tradesTable tbody').innerHTML = 
                '<tr><td colspan="8" class="text-center text-danger">加载失败</td></tr>';
        }
    }

    async loadAlerts() {
        // 这里需要实现警报记录加载逻辑
        const tbody = document.querySelector('#alertsTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">功能开发中...</td></tr>';
    }

    async loadSymbolConfigs() {
        try {
            const response = await this.apiCall('/api/config/symbols');
            const configs = response.data;

            const container = document.getElementById('symbolConfigContainer');
            
            container.innerHTML = Object.entries(configs).map(([symbol, config]) => `
                <div class="row mb-3 p-3 border rounded">
                    <div class="col-md-2">
                        <strong>${symbol}</strong>
                        <br>
                        <small class="text-muted">${config.category}</small>
                    </div>
                    <div class="col-md-2">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enabled_${symbol}" ${config.enabled ? 'checked' : ''}>
                            <label class="form-check-label" for="enabled_${symbol}">启用</label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">手数</label>
                        <input type="number" class="form-control form-control-sm" id="lot_${symbol}" value="${config.lot_size}" step="0.01">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">止损(USD)</label>
                        <input type="number" class="form-control form-control-sm" id="sl_${symbol}" value="${config.stop_loss_usd}" step="1">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">止盈(USD)</label>
                        <input type="number" class="form-control form-control-sm" id="tp_${symbol}" value="${config.take_profit_usd}" step="1">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">超时(秒)</label>
                        <input type="number" class="form-control form-control-sm" id="timeout_${symbol}" value="${config.signal_timeout}" step="1">
                    </div>
                </div>
            `).join('');

        } catch (error) {
            console.error('加载交易对配置失败:', error);
            document.getElementById('symbolConfigContainer').innerHTML = 
                '<div class="text-center text-danger">加载失败</div>';
        }
    }

    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadSystemStatus();
            this.loadPositions();
        }, 10000); // 每10秒刷新一次
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    bindEvents() {
        // 标签页切换事件
        document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (event) => {
                const target = event.target.getAttribute('data-bs-target');
                if (target === '#trades') {
                    this.loadTrades();
                } else if (target === '#alerts') {
                    this.loadAlerts();
                } else if (target === '#config') {
                    this.loadSymbolConfigs();
                }
            });
        });
    }

    formatCurrency(amount) {
        if (amount === null || amount === undefined) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    formatDateTime(dateStr) {
        if (!dateStr) return 'N/A';
        return new Date(dateStr).toLocaleString('zh-CN');
    }

    showAlert(type, message) {
        // 创建警告提示
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 5秒后自动移除
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    logout() {
        localStorage.removeItem('session_token');
        sessionStorage.removeItem('session_token');
        window.location.href = '/login';
    }

    async showConfigModal() {
        try {
            // 获取系统配置
            const response = await this.apiCall('/api/config/system');
            const config = response.data;

            // 创建配置模态框
            const modalHtml = `
                <div class="modal fade" id="configModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-gear"></i> 系统配置
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <!-- 配置标签页 -->
                                <ul class="nav nav-tabs mb-3" id="configModalTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="trading-tab" data-bs-toggle="tab" data-bs-target="#trading-config" type="button" role="tab">
                                            📊 交易配置
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="risk-tab" data-bs-toggle="tab" data-bs-target="#risk-config" type="button" role="tab">
                                            🛡️ 风险控制
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="notification-tab" data-bs-toggle="tab" data-bs-target="#notification-config" type="button" role="tab">
                                            🔔 通知设置
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system-config" type="button" role="tab">
                                            🔧 系统设置
                                        </button>
                                    </li>
                                </ul>

                                <form id="configForm">
                                    <div class="tab-content" id="configModalTabContent">
                                        <!-- 交易配置 -->
                                        <div class="tab-pane fade show active" id="trading-config" role="tabpanel">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">交易冷却时间 (秒)</label>
                                                        <input type="number" class="form-control" name="trading_cooldown"
                                                               value="${config.trading_cooldown || 300}" min="60" step="30">
                                                        <div class="form-text">两次交易之间的最小间隔时间</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">最大并发交易数</label>
                                                        <input type="number" class="form-control" name="max_concurrent_trades"
                                                               value="${config.max_concurrent_trades || 5}" min="1" max="20">
                                                        <div class="form-text">同时持有的最大订单数量</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="auto_trading_enabled"
                                                                   ${config.auto_trading_enabled !== false ? 'checked' : ''}>
                                                            <label class="form-check-label">启用自动交易</label>
                                                        </div>
                                                        <div class="form-text">关闭后将只记录警报，不执行交易</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="emergency_stop_enabled"
                                                                   ${config.emergency_stop_enabled !== false ? 'checked' : ''}>
                                                            <label class="form-check-label">启用紧急停止</label>
                                                        </div>
                                                        <div class="form-text">允许一键平仓所有订单</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 风险控制 -->
                                        <div class="tab-pane fade" id="risk-config" role="tabpanel">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">日最大亏损 (USD)</label>
                                                        <input type="number" class="form-control" name="max_daily_loss_usd"
                                                               value="${config.max_daily_loss_usd || 500}" min="50" step="50">
                                                        <div class="form-text">达到此亏损额度将停止交易</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">日最大盈利 (USD)</label>
                                                        <input type="number" class="form-control" name="max_daily_profit_usd"
                                                               value="${config.max_daily_profit_usd || 1000}" min="100" step="100">
                                                        <div class="form-text">达到此盈利额度将停止交易</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">全局止损 (USD)</label>
                                                        <input type="number" class="form-control" name="global_stop_loss_usd"
                                                               value="${config.global_stop_loss_usd || 500}" min="50" step="50">
                                                        <div class="form-text">组合总亏损达到此额度将全部平仓</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">全局止盈 (USD)</label>
                                                        <input type="number" class="form-control" name="global_take_profit_usd"
                                                               value="${config.global_take_profit_usd || 1000}" min="100" step="100">
                                                        <div class="form-text">组合总盈利达到此额度将全部平仓</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">风险检查间隔 (秒)</label>
                                                        <input type="number" class="form-control" name="risk_check_interval"
                                                               value="${config.risk_check_interval || 30}" min="10" step="10">
                                                        <div class="form-text">风险监控检查频率</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">余额记录间隔 (分钟)</label>
                                                        <input type="number" class="form-control" name="balance_record_interval"
                                                               value="${config.balance_record_interval || 5}" min="1" step="1">
                                                        <div class="form-text">账户余额记录频率</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 通知设置 -->
                                        <div class="tab-pane fade" id="notification-config" role="tabpanel">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="bark_forward_enabled"
                                                                   ${config.bark_forward_enabled === 'True' ? 'checked' : ''}>
                                                            <label class="form-check-label">启用Bark通知转发</label>
                                                        </div>
                                                        <div class="form-text">将警报转发到Bark推送服务</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="sound_enabled"
                                                                   ${config.sound_enabled !== false ? 'checked' : ''}>
                                                            <label class="form-check-label">启用声音提示</label>
                                                        </div>
                                                        <div class="form-text">交易和警报时播放提示音</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 系统设置 -->
                                        <div class="tab-pane fade" id="system-config" role="tabpanel">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">界面主题</label>
                                                        <select class="form-select" name="ui_theme">
                                                            <option value="light" ${config.ui_theme === 'light' ? 'selected' : ''}>浅色主题</option>
                                                            <option value="dark" ${config.ui_theme === 'dark' ? 'selected' : ''}>深色主题</option>
                                                            <option value="auto" ${config.ui_theme === 'auto' ? 'selected' : ''}>跟随系统</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">数据刷新间隔 (秒)</label>
                                                        <select class="form-select" name="refresh_interval">
                                                            <option value="10" ${config.refresh_interval == 10 ? 'selected' : ''}>10秒</option>
                                                            <option value="30" ${config.refresh_interval == 30 ? 'selected' : ''}>30秒</option>
                                                            <option value="60" ${config.refresh_interval == 60 ? 'selected' : ''}>60秒</option>
                                                            <option value="120" ${config.refresh_interval == 120 ? 'selected' : ''}>2分钟</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">日志级别</label>
                                                        <select class="form-select" name="log_level">
                                                            <option value="DEBUG" ${config.log_level === 'DEBUG' ? 'selected' : ''}>DEBUG - 详细调试</option>
                                                            <option value="INFO" ${config.log_level === 'INFO' ? 'selected' : ''}>INFO - 一般信息</option>
                                                            <option value="WARNING" ${config.log_level === 'WARNING' ? 'selected' : ''}>WARNING - 警告信息</option>
                                                            <option value="ERROR" ${config.log_level === 'ERROR' ? 'selected' : ''}>ERROR - 错误信息</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="mb-3">
                                                        <label class="form-label">数据库备份间隔</label>
                                                        <select class="form-select" name="backup_interval">
                                                            <option value="6" ${config.backup_interval == 6 ? 'selected' : ''}>6小时</option>
                                                            <option value="12" ${config.backup_interval == 12 ? 'selected' : ''}>12小时</option>
                                                            <option value="24" ${config.backup_interval == 24 ? 'selected' : ''}>24小时</option>
                                                            <option value="168" ${config.backup_interval == 168 ? 'selected' : ''}>7天</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="ui.saveSystemConfig()">
                                    <i class="bi bi-check-lg"></i> 保存配置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除旧的模态框
            const oldModal = document.getElementById('configModal');
            if (oldModal) {
                oldModal.remove();
            }

            // 添加新的模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('configModal'));
            modal.show();

        } catch (error) {
            console.error('加载系统配置失败:', error);
            this.showAlert('danger', '加载系统配置失败: ' + error.message);
        }
    }

    async saveSystemConfig() {
        try {
            const form = document.getElementById('configForm');
            const formData = new FormData(form);

            const config = {};
            for (let [key, value] of formData.entries()) {
                if (form.querySelector(`[name="${key}"]`).type === 'checkbox') {
                    config[key] = form.querySelector(`[name="${key}"]`).checked ? 'True' : 'False';
                } else {
                    config[key] = value;
                }
            }

            const response = await this.apiCall('/api/config/system', 'PUT', config);

            if (response.success) {
                this.showAlert('success', '系统配置保存成功');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
                modal.hide();

                // 刷新数据
                await this.loadSystemStatus();
            } else {
                throw new Error(response.error || '保存配置失败');
            }

        } catch (error) {
            console.error('保存系统配置失败:', error);
            this.showAlert('danger', '保存系统配置失败: ' + error.message);
        }
    }
}

// 全局函数
let ui;

document.addEventListener('DOMContentLoaded', () => {
    ui = new TradingSystemUI();
});

async function startStrategy() {
    try {
        await ui.apiCall('/api/strategy/start', 'POST');
        ui.showAlert('success', '策略启动成功');
        ui.loadSystemStatus();
    } catch (error) {
        ui.showAlert('danger', '策略启动失败: ' + error.message);
    }
}

async function stopStrategy() {
    try {
        await ui.apiCall('/api/strategy/stop', 'POST');
        ui.showAlert('warning', '策略已停止');
        ui.loadSystemStatus();
    } catch (error) {
        ui.showAlert('danger', '策略停止失败: ' + error.message);
    }
}

async function emergencyStop() {
    if (!confirm('确定要执行紧急停止吗？这将平仓所有订单！')) {
        return;
    }
    
    try {
        await ui.apiCall('/api/strategy/emergency-stop', 'POST');
        ui.showAlert('danger', '紧急停止已执行');
        ui.loadSystemStatus();
        ui.loadPositions();
    } catch (error) {
        ui.showAlert('danger', '紧急停止失败: ' + error.message);
    }
}

async function closePosition(ticket) {
    if (!confirm(`确定要平仓订单 #${ticket} 吗？`)) {
        return;
    }
    
    try {
        await ui.apiCall(`/api/positions/${ticket}/close`, 'POST', {
            reason: '手动平仓'
        });
        ui.showAlert('success', '订单平仓成功');
        ui.loadPositions();
        ui.loadSystemStatus();
    } catch (error) {
        ui.showAlert('danger', '平仓失败: ' + error.message);
    }
}

function refreshData() {
    ui.loadInitialData();
    ui.showAlert('info', '数据已刷新');
}

function showConfigModal() {
    if (ui) {
        ui.showConfigModal();
    }
}

function logout() {
    ui.logout();
}

// 移动端表格滚动优化
function initMobileTableScroll() {
    // 检测是否为移动设备
    function isMobile() {
        return window.innerWidth <= 768;
    }

    // 强制设置表格容器为可滚动
    function forceTableScrollable() {
        if (!isMobile()) return;

        // 查找所有表格容器
        const containers = [
            ...document.querySelectorAll('.table-responsive'),
            ...document.querySelectorAll('div:has(> table.table)'),
            ...document.querySelectorAll('[id*="Table"]').map(el => el.closest('div')).filter(Boolean)
        ];

        // 去重
        const uniqueContainers = [...new Set(containers)];

        uniqueContainers.forEach(container => {
            if (!container) return;

            const table = container.querySelector('table');
            if (table) {
                // 强制设置容器样式
                container.style.overflowX = 'auto';
                container.style.overflowY = 'hidden';
                container.style.webkitOverflowScrolling = 'touch';
                container.style.maxWidth = '100%';
                container.style.position = 'relative';

                // 强制设置表格样式
                table.style.minWidth = '800px';
                table.style.width = 'auto';
                table.style.tableLayout = 'auto';

                // 特殊处理持仓表格
                if (table.id === 'positionsTable' ||
                    container.querySelector('#positionsDetailTableBody') ||
                    table.querySelector('th:contains("订单号")') ||
                    table.querySelector('th:contains("交易对")')) {
                    table.style.minWidth = '900px';
                }

                // 检查是否需要滚动提示
                const containerWidth = container.clientWidth;
                const tableWidth = table.scrollWidth;

                if (tableWidth > containerWidth) {
                    container.setAttribute('data-scrollable', 'true');

                    // 添加触摸滚动提示
                    if (!container.querySelector('.scroll-hint')) {
                        const hint = document.createElement('div');
                        hint.className = 'scroll-hint';
                        hint.style.cssText = `
                            position: absolute;
                            bottom: -25px;
                            left: 50%;
                            transform: translateX(-50%);
                            font-size: 0.75rem;
                            color: #6c757d;
                            white-space: nowrap;
                            pointer-events: none;
                            opacity: 0.8;
                            z-index: 1;
                            background: rgba(255,255,255,0.9);
                            padding: 2px 8px;
                            border-radius: 4px;
                            border: 1px solid #dee2e6;
                        `;
                        hint.textContent = '← 左右滑动查看更多 →';
                        container.style.position = 'relative';
                        container.appendChild(hint);

                        // 5秒后隐藏提示
                        setTimeout(() => {
                            if (hint.parentNode) {
                                hint.style.opacity = '0';
                                setTimeout(() => {
                                    if (hint.parentNode) {
                                        hint.remove();
                                    }
                                }, 500);
                            }
                        }, 5000);
                    }
                } else {
                    container.removeAttribute('data-scrollable');
                }

                console.log(`表格容器已优化: ${table.id || 'unnamed'}, 容器宽度: ${containerWidth}, 表格宽度: ${tableWidth}`);
            }
        });
    }

    // 页面加载完成后检查
    function initCheck() {
        setTimeout(forceTableScrollable, 100);
        setTimeout(forceTableScrollable, 500);
        setTimeout(forceTableScrollable, 1000);
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initCheck);
    } else {
        initCheck();
    }

    // 窗口大小改变时重新检查
    window.addEventListener('resize', () => {
        setTimeout(forceTableScrollable, 100);
    });

    // 监听表格内容变化
    const observer = new MutationObserver(() => {
        setTimeout(forceTableScrollable, 100);
    });
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'id']
    });

    // 监听页面切换
    window.addEventListener('hashchange', () => {
        setTimeout(forceTableScrollable, 200);
    });
}

// 专门处理持仓表格的移动端优化
function optimizePositionTables() {
    if (window.innerWidth > 768) return;

    // 查找所有持仓相关的表格
    const positionTables = [
        document.getElementById('positionsTable'),
        document.querySelector('#positionsDetailTableBody')?.closest('table'),
        ...document.querySelectorAll('table:has(th:contains("交易对"))'),
        ...document.querySelectorAll('table:has(th:contains("订单号"))')
    ].filter(Boolean);

    positionTables.forEach(table => {
        const container = table.closest('.table-responsive') || table.parentElement;

        if (container) {
            // 强制设置容器样式
            container.style.cssText += `
                overflow-x: auto !important;
                overflow-y: hidden !important;
                -webkit-overflow-scrolling: touch !important;
                max-width: 100% !important;
                display: block !important;
            `;

            // 强制设置表格样式
            table.style.cssText += `
                min-width: 900px !important;
                width: auto !important;
                table-layout: auto !important;
            `;

            // 强制显示所有隐藏的列
            const hiddenCells = table.querySelectorAll('.d-none, .d-md-table-cell, .d-lg-table-cell, .d-sm-table-cell');
            hiddenCells.forEach(cell => {
                cell.style.display = 'table-cell !important';
                cell.classList.remove('d-none');
            });

            // 优化操作列
            const lastCells = table.querySelectorAll('th:last-child, td:last-child');
            lastCells.forEach(cell => {
                cell.style.cssText += `
                    position: sticky !important;
                    right: 0 !important;
                    background-color: #fff !important;
                    z-index: 10 !important;
                    border-left: 2px solid #dee2e6 !important;
                    box-shadow: -2px 0 4px rgba(0,0,0,0.1) !important;
                    min-width: 120px !important;
                `;
            });

            console.log('持仓表格已优化:', table.id || 'unnamed');
        }
    });
}

// 初始化移动端表格滚动
initMobileTableScroll();

// 专门优化持仓表格
setTimeout(optimizePositionTables, 500);
setTimeout(optimizePositionTables, 1500);

// 监听页面变化，重新优化表格
const originalShowPage = window.showPage;
if (originalShowPage) {
    window.showPage = function(page) {
        originalShowPage(page);
        setTimeout(optimizePositionTables, 300);
        setTimeout(initMobileTableScroll, 300);
        setTimeout(forceTableScrollFix, 500);
    };
}

// 强制修复表格滚动问题的终极解决方案
function forceTableScrollFix() {
    if (window.innerWidth > 768) return;

    console.log('执行强制表格滚动修复...');

    // 查找所有可能的表格容器
    const allContainers = [
        ...document.querySelectorAll('.table-responsive'),
        ...document.querySelectorAll('div:has(table)'),
        ...document.querySelectorAll('[class*="table"]'),
        ...Array.from(document.querySelectorAll('table')).map(t => t.parentElement)
    ];

    // 去重并过滤
    const uniqueContainers = [...new Set(allContainers)].filter(Boolean);

    uniqueContainers.forEach((container, index) => {
        const table = container.querySelector('table');
        if (!table) return;

        console.log(`修复表格容器 ${index + 1}:`, container.className || 'no-class');

        // 直接设置内联样式，优先级最高
        container.setAttribute('style', `
            overflow-x: auto !important;
            overflow-y: hidden !important;
            -webkit-overflow-scrolling: touch !important;
            max-width: 100% !important;
            display: block !important;
            position: relative !important;
        `);

        table.setAttribute('style', `
            min-width: 850px !important;
            width: auto !important;
            table-layout: auto !important;
        `);

        // 强制显示所有隐藏的单元格
        const hiddenCells = table.querySelectorAll('.d-none, [class*="d-md-"], [class*="d-lg-"], [class*="d-sm-"]');
        hiddenCells.forEach(cell => {
            cell.style.display = 'table-cell !important';
            cell.style.visibility = 'visible !important';
        });

        // 优化最后一列（操作列）
        const lastCells = table.querySelectorAll('th:last-child, td:last-child');
        lastCells.forEach(cell => {
            cell.setAttribute('style', `
                position: sticky !important;
                right: 0 !important;
                background-color: #fff !important;
                z-index: 10 !important;
                border-left: 2px solid #dee2e6 !important;
                box-shadow: -2px 0 4px rgba(0,0,0,0.1) !important;
                min-width: 120px !important;
            `);
        });

        // 优化表头
        const headerCells = table.querySelectorAll('thead th');
        headerCells.forEach(cell => {
            cell.style.position = 'sticky';
            cell.style.top = '0';
            cell.style.backgroundColor = '#f8f9fa';
            cell.style.zIndex = '5';
        });

        // 最后一列表头特殊处理
        const lastHeaderCell = table.querySelector('thead th:last-child');
        if (lastHeaderCell) {
            lastHeaderCell.style.zIndex = '15';
        }

        // 添加滚动提示
        if (!container.querySelector('.mobile-scroll-hint')) {
            const hint = document.createElement('div');
            hint.className = 'mobile-scroll-hint';
            hint.style.cssText = `
                position: absolute;
                bottom: -30px;
                left: 50%;
                transform: translateX(-50%);
                font-size: 12px;
                color: #6c757d;
                background: rgba(255,255,255,0.9);
                padding: 4px 12px;
                border-radius: 6px;
                border: 1px solid #dee2e6;
                white-space: nowrap;
                z-index: 1;
                opacity: 0.8;
            `;
            hint.textContent = '← 左右滑动查看更多 →';
            container.appendChild(hint);

            // 5秒后隐藏提示
            setTimeout(() => {
                if (hint.parentNode) {
                    hint.style.opacity = '0';
                    setTimeout(() => hint.remove(), 500);
                }
            }, 5000);
        }
    });

    console.log(`已修复 ${uniqueContainers.length} 个表格容器`);
}

// 页面加载后立即执行修复
setTimeout(forceTableScrollFix, 1000);
setTimeout(forceTableScrollFix, 3000);

// 监听DOM变化，自动修复新添加的表格
const tableObserver = new MutationObserver(() => {
    setTimeout(forceTableScrollFix, 200);
});

tableObserver.observe(document.body, {
    childList: true,
    subtree: true
});
